import http from "@/services/http";

export class AuthService {
  static async register(data: {
    email: string;
    password: string;
    name: string;
    loginType: string;
  }) {
    console.log("Registering user", data);
    return http.post("/user/register", data);
  }

  static async login(data: { email: string; password: string }) {
    return http.post("/user/login", data);
  }

  static async sendOtp(email: string) {
    return http.post("/user/sendotp", { email });
  }

  static async verifyOtp(data: { email: string; otp: string; userotp: string }) {
    return http.post("/user/verifyotp", data);
  }
}
