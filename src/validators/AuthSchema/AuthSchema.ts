// src/validators/authSchemas.ts
import { z } from "zod";

export const signupSchema = z.object({
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string().min(6, "Confirm password must be at least 6 characters"),
}).refine((data) => data.password === data.confirmPassword, {
  path: ["confirmPassword"],
  message: "Passwords do not match",
});


export const verifyOtpSchema = z.object({
  email: z.string().email("Invalid email address"),
  otp: z.string().min(6, "OTP must be at least 6 characters"),
  userOtp: z.string().min(6, "OTP must be at least 6 characters"),
}).refine((data) => data.otp === data.userOtp, {
  path: ["userOtp"],
  message: "OTP does not match",
});


export const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});


export type SignupFormData = z.infer<typeof signupSchema>;
export type VerifyOtpFormData = z.infer<typeof verifyOtpSchema>;
export type LoginFormData = z.infer<typeof loginSchema>;