@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Light theme - East Classified colors */

  /* East Classified brand colors */
  --primary: #033365;
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: #ffffff;
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  /* East Classified blue palette */
  --east-blue-50: #f0f9ff;
  --east-blue-100: #e0f2fe;
  --east-blue-200: #bae6fd;
  --east-blue-300: #7dd3fc;
  --east-blue-400: #38bdf8;
  --east-blue-500: #0ea5e9;
  --east-blue-600: #0284c7;
  --east-blue-700: #0369a1;
  --east-blue-800: #075985;
  --east-blue-900: #0c4a6e;
  --east-blue-950: #033365;

  /* Typography */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Border radius */
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Dark theme */

    /* Adjusted colors for dark theme */
    --primary: #033365;
    --primary-foreground: #0a0a0a;
    --secondary: #1e293b;
    --secondary-foreground: #f1f5f9;
    --muted: #0f172a;
    --muted-foreground: #94a3b8;
    --accent: #1e293b;
    --accent-foreground: #f1f5f9;
    --destructive: #dc2626;
    --destructive-foreground: #f1f5f9;
    --border: #334155;
    --input: #334155;
    --ring: #ffffff;
    --card: #0f172a;
    --card-foreground: #f1f5f9;
    --popover: #0f172a;
    --popover-foreground: #f1f5f9;
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  /* East Classified colors */
  --color-east-blue-50: var(--east-blue-50);
  --color-east-blue-100: var(--east-blue-100);
  --color-east-blue-200: var(--east-blue-200);
  --color-east-blue-300: var(--east-blue-300);
  --color-east-blue-400: var(--east-blue-400);
  --color-east-blue-500: var(--east-blue-500);
  --color-east-blue-600: var(--east-blue-600);
  --color-east-blue-700: var(--east-blue-700);
  --color-east-blue-800: var(--east-blue-800);
  --color-east-blue-900: var(--east-blue-900);
  --color-east-blue-950: var(--east-blue-950);

  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --border-radius: var(--radius);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

body {
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  line-height: 1.6;
}

/* East Classified component styles */
@layer components {
  .east-gradient {
    background: linear-gradient(135deg, var(--east-blue-700) 0%, var(--east-blue-600) 100%);
  }

  .east-hero-gradient {
    background: linear-gradient(135deg, var(--east-blue-800) 0%, var(--east-blue-900) 50%, var(--east-blue-950) 100%);
  }

  .east-card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .east-card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(3, 105, 161, 0.1), 0 10px 10px -5px rgba(3, 105, 161, 0.04);
  }

  .east-button-primary {
    background: var(--east-blue-700);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 500;
    transition: background-color 0.2s ease;
  }

  .east-button-primary:hover {
    background: var(--east-blue-800);
  }

  .east-button-secondary {
    background: white;
    color: var(--east-blue-700);
    border: 1px solid var(--east-blue-200);
    padding: 0.5rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .east-button-secondary:hover {
    background: var(--east-blue-50);
  }
}

/* Utility classes for East Classified */
@layer utilities {
  .text-east-blue {
    color: var(--east-blue-700);
  }

  .bg-east-blue {
    background-color: var(--east-blue-700);
  }

  .border-east-blue {
    border-color: var(--east-blue-200);
  }

  .east-shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(3, 105, 161, 0.05);
  }

  .east-shadow {
    box-shadow: 0 4px 6px -1px rgba(3, 105, 161, 0.1), 0 2px 4px -1px rgba(3, 105, 161, 0.06);
  }

  .east-shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(3, 105, 161, 0.1), 0 4px 6px -2px rgba(3, 105, 161, 0.05);
  }
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}