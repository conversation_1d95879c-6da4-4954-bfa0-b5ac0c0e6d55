"use client";

import React, { useState } from "react";
import SignUpComponent from "@/components/AuthComponent/SignUpComponent";
import {
  signupSchema,
  SignupFormData,
} from "@/validators/AuthSchema/AuthSchema";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import { AuthService } from "@/services/auth/AuthService";
export default function SignupPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<SignupFormData>({
    fullName: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    const result = signupSchema.safeParse(formData);

    if (!result.success) {
      result.error.issues.forEach((issue) => {
        console.log(`${issue.path.join(".")}: ${issue.message}`);
      });

      const errorMessages = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join("\n");

      toast.error(errorMessages);
      return;
    }

    try {
      setIsLoading(true);
      await AuthService.register({
        email: formData.email,
        password: formData.password,
        name: formData.fullName,
        loginType: "email",
      })
        .then((res) => {
          toast.success("Account created successfully");
          setFormData({
            fullName: "",
            email: "",
            password: "",
            confirmPassword: "",
          });

          AuthService.sendOtp(formData.email)
            .then((res) => {
              setIsLoading(false);
              localStorage.setItem("email", formData.email);
              localStorage.setItem("otp", res.data.otp);
              router.push("/verifyotp");
              toast.success("OTP sent successfully");
            })
            .catch((err) => {
              setIsLoading(false);
              toast.error(err.response.data.message);
            });
        })
        .catch((err) => {
          setIsLoading(false);
          toast.error(err.response.data.message);
        });
    } catch (error) {
      setIsLoading(false);
      toast.error("An error occurred while submitting the form.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SignUpComponent
      isLoading={isLoading}
      formData={formData}
      handleInputChange={handleInputChange}
      handleSubmit={handleSubmit}
      showPassword={showPassword}
      showConfirmPassword={showConfirmPassword}
      setShowPassword={setShowPassword}
      setShowConfirmPassword={setShowConfirmPassword}
    />
  );
}
