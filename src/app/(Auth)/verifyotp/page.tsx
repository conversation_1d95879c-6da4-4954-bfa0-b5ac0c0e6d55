"use client";

import React, { useState } from "react";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import { AuthService } from "@/services/auth/AuthService";
import VerifyOtpComponent from "@/components/AuthComponent/VerifyOtpComponent";
import {
  verifyOtpSchema,
  VerifyOtpFormData,
} from "@/validators/AuthSchema/AuthSchema";

export default function VerifyOtpPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<VerifyOtpFormData>({
    email: "",
    otp: "",
    userOtp: "",
  });
  const [showOtp, setShowOtp] = useState(false);
  const [showUserOtp, setShowUserOtp] = useState(false);
  const router = useRouter();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      formData.email = localStorage.getItem("email") ?? "";
      formData.otp = localStorage.getItem("otp") ?? "";
      const result = verifyOtpSchema.safeParse(formData);

      if (!result.success) {
        result.error.issues.forEach((issue) => {
          console.log(`${issue.path.join(".")}: ${issue.message}`);
        });

        const errorMessages = result.error.issues
          .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
          .join("\n");

        toast.error(errorMessages);
        return;
      }
      await AuthService.verifyOtp({
        email: formData.email,
        otp: formData.otp,
        userotp: formData.userOtp,
      })
        .then((res) => {
          toast.success("OTP verified successfully");
          router.replace("/login");
        })
        .catch((err) => {
          toast.error(err.response.data.message);
        });
    } catch (error) {
      toast.error("An error occurred while submitting the form.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <VerifyOtpComponent
      isLoading={isLoading}
      formData={formData}
      handleInputChange={handleInputChange}
      handleSubmit={handleSubmit}
      showOtp={showOtp}
      showUserOtp={showUserOtp}
      setShowOtp={setShowOtp}
      setShowUserOtp={setShowUserOtp}
    />
  );
}
