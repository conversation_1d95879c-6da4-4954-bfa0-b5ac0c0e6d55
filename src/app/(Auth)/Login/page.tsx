"use client";

import React, { useState } from "react";
import { loginSchema, LoginFormData } from "@/validators/AuthSchema/AuthSchema";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import { AuthService } from "@/services/auth/AuthService";
import LoginComponent from "@/components/AuthComponent/LoginComponent";
export default function LoginPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<LoginFormData>({
    email: "",
    password: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    const result = loginSchema.safeParse(formData);

    if (!result.success) {
      result.error.issues.forEach((issue) => {
        console.log(`${issue.path.join(".")}: ${issue.message}`);
      });

      const errorMessages = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join("\n");

      toast.error(errorMessages);
      return;
    }

    try {
      setIsLoading(true);
      await AuthService.login({
        email: formData.email,
        password: formData.password,
      }).then((res) => {
        localStorage.setItem("token", res.data.token);
        localStorage.setItem("user", JSON.stringify(res.data.user));
        toast.success("Login successful");
        router.push("/");
      }).catch((err) => {
        toast.error(err.response.data.message);
      })
    } catch (error) {
      setIsLoading(false);
      toast.error("Login failed");
    }

  };

  return (
    <LoginComponent
      isLoading={isLoading}
      formData={formData}
      handleInputChange={handleInputChange}
      handleSubmit={handleSubmit}
      showPassword={showPassword}
      showConfirmPassword={showConfirmPassword}
      setShowPassword={setShowPassword}
      setShowConfirmPassword={setShowConfirmPassword}
    />
  );
}
