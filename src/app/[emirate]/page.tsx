import NavBarComponent from "@/components/NavbarComponent/NavbarComponent";
import CategoryFilterNavBar from "@/components/CategoryFilterNavBar/CategoryFilterNavBar";
import FooterComponent from "@/components/FooterComponent/FooterComponent";
import HomeSearchComponent from "@/components/HomeSearchComponent/HomeSearchComponent";
import ProductCard from "@/components/HomeProductCardComponent/HomeProductCardComponent";
import HomeProductHeadingComponent from "@/components/HomeProductCardComponent/HomeProductHeadingComponent";

export default async function EmiratePage({
  params,
}: {
  params: Promise<{ emirate: string }>;
}) {
  // const { emirate } = await params;

  return (
    <div className="min-h-screen flex flex-col">
      <NavBarComponent />
      <CategoryFilterNavBar />
      <HomeSearchComponent />

      {/* Products Grid Container */}
      <div className="flex-1 py-12">
        <div className="max-w-7xl mx-auto px-4">
          {/* Popular Residential Properties */}
          <div className="mb-16">
            <HomeProductHeadingComponent title="Popular Residential Properties" />
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              <ProductCard
                id="101"
                title="Luxury 2 Bedroom Apartment in Dubai Marina"
                price={4500}
                currency="AED"
                location="Dubai Marina, Dubai"
                images={[
                  "/images/eastclassified.jpeg",
                  "/images/eastclassified.jpeg",
                ]}
                category="property"
                bedrooms={2}
                bathrooms={2}
                area={1200}
                areaUnit="sqft"
                featured
                verified
                postedTime="2 hours ago"
                views={89}
                isFavorite={false}
              />

              <ProductCard
                id="102"
                title="Modern 3BR Villa with Private Pool"
                price={8500}
                currency="AED"
                location="Arabian Ranches, Dubai"
                images={[
                  "/images/eastclassified.jpeg",
                  "/images/eastclassified.jpeg",
                ]}
                category="property"
                bedrooms={3}
                bathrooms={3}
                area={2800}
                areaUnit="sqft"
                featured={false}
                verified
                postedTime="1 hour ago"
                views={156}
                isFavorite={false}
              />

              <ProductCard
                id="103"
                title="Executive Penthouse Downtown"
                price={12000}
                currency="AED"
                location="Downtown Dubai, Dubai"
                images={[
                  "/images/eastclassified.jpeg",
                  "/images/eastclassified.jpeg",
                ]}
                category="property"
                bedrooms={4}
                bathrooms={4}
                area={3200}
                areaUnit="sqft"
                featured
                verified
                postedTime="3 hours ago"
                views={234}
                isFavorite={false}
              />
            </div>
          </div>

          {/* Popular Cars */}
          <div className="mb-16">
            <HomeProductHeadingComponent title="Popular Cars" />
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              <ProductCard
                id="201"
                title="2022 Toyota Camry - Excellent Condition"
                price={65000}
                currency="AED"
                location="Dubai Marina, Dubai"
                images={[
                  "/images/eastclassified.jpeg",
                  "/images/eastclassified.jpeg",
                ]}
                category="cars"
                year={2022}
                brand="Toyota"
                mileage={15000}
                featured
                verified
                postedTime="2 hours ago"
                views={145}
                isFavorite={false}
              />

              <ProductCard
                id="202"
                title="2021 BMW X5 - Full Option"
                price={185000}
                currency="AED"
                location="DIFC, Dubai"
                images={[
                  "/images/eastclassified.jpeg",
                  "/images/eastclassified.jpeg",
                ]}
                category="cars"
                year={2021}
                brand="BMW"
                mileage={28000}
                featured
                verified
                postedTime="1 hour ago"
                views={203}
                isFavorite={false}
              />

              <ProductCard
                id="203"
                title="2023 Nissan Patrol Platinum"
                price={220000}
                currency="AED"
                location="Dubai Hills, Dubai"
                images={[
                  "/images/eastclassified.jpeg",
                  "/images/eastclassified.jpeg",
                ]}
                category="cars"
                year={2023}
                brand="Nissan"
                mileage={5000}
                featured={false}
                verified
                postedTime="4 hours ago"
                views={178}
                isFavorite={false}
              />
            </div>
          </div>

          {/* Latest Jobs */}
          <div className="mb-16">
            <HomeProductHeadingComponent title="Latest Jobs" />
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              <ProductCard
                id="301"
                title="Senior Software Engineer"
                price={0}
                salary="AED 18,000 - 25,000"
                currency="AED"
                location="Dubai Internet City, Dubai"
                images={["/images/eastclassified.jpeg"]}
                category="jobs"
                company="TechCorp Solutions"
                jobType="full-time"
                featured
                verified
                postedTime="3 hours ago"
                views={127}
                isFavorite={false}
              />

              <ProductCard
                id="302"
                title="Marketing Manager"
                price={0}
                salary="AED 12,000 - 18,000"
                currency="AED"
                location="Dubai Media City, Dubai"
                images={["/images/eastclassified.jpeg"]}
                category="jobs"
                company="Digital Dynamics"
                jobType="full-time"
                featured={false}
                verified
                postedTime="1 day ago"
                views={98}
                isFavorite={false}
              />

              <ProductCard
                id="303"
                title="Finance Director"
                price={0}
                salary="AED 35,000 - 45,000"
                currency="AED"
                location="DIFC, Dubai"
                images={["/images/eastclassified.jpeg"]}
                category="jobs"
                company="Emirates Finance Group"
                jobType="full-time"
                featured
                verified
                postedTime="8 hours ago"
                views={167}
                isFavorite={false}
              />
            </div>
          </div>

          {/* Popular Electronics */}
          <div className="mb-16">
            <HomeProductHeadingComponent title="Popular Electronics" />
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              <ProductCard
                id="401"
                title="iPhone 15 Pro Max 256GB - Brand New"
                price={4850}
                currency="AED"
                location="Dubai Mall, Dubai"
                images={[
                  "/images/eastclassified.jpeg",
                  "/images/eastclassified.jpeg",
                ]}
                category="electronics"
                condition="new"
                featured
                verified
                postedTime="30 minutes ago"
                views={234}
                isFavorite={false}
              />

              <ProductCard
                id="402"
                title="MacBook Pro M3 16-inch"
                price={8500}
                currency="AED"
                location="City Walk, Dubai"
                images={[
                  "/images/eastclassified.jpeg",
                  "/images/eastclassified.jpeg",
                ]}
                category="electronics"
                condition="excellent"
                featured
                verified
                postedTime="1 hour ago"
                views={189}
                isFavorite={false}
              />

              <ProductCard
                id="403"
                title="Samsung Galaxy S24 Ultra"
                price={4200}
                currency="AED"
                location="Mall of Emirates, Dubai"
                images={[
                  "/images/eastclassified.jpeg",
                  "/images/eastclassified.jpeg",
                ]}
                category="electronics"
                condition="excellent"
                featured={false}
                verified
                postedTime="2 hours ago"
                views={156}
                isFavorite={false}
              />
            </div>
          </div>
        </div>
      </div>

      <FooterComponent />
    </div>
  );
}
