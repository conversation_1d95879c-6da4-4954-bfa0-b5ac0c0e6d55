import AdsCardComponent from "@/components/AdsCardComponent/AdsCardComponent";
import CategoryFilterNavBar from "@/components/CategoryFilterNavBar/CategoryFilterNavBar";
import FooterComponent from "@/components/FooterComponent/FooterComponent";
import ItemCounterComponent from "@/components/ItemCounterComponent/ItemCounterComponent";
import MainCategorySearchComponent from "@/components/MainCategorySearchFilterComponent/MainCategorySearchFilterComponent";
import NavBarComponent from "@/components/NavbarComponent/NavbarComponent";
import { CarAd, ElectronicsAd, PropertyAd } from "@/model/adsModel";
import { CounterItem } from "@/model/counterModel";
import { useState } from "react";

export default async function CategoryPage({
  params,
}: {
  params: Promise<{ category: string }>;
}) {
  const { category } = await params;
  let bgImageUrl = "/images/moters.jpeg";

  switch (category) {
    case "motors":
      bgImageUrl = "/images/moters.jpeg";
      break;
    case "property":
      bgImageUrl = "/images/property.jpeg";
      break;
    default:
      bgImageUrl = "/images/moters.jpeg";
      break;
  }

  const defaultItems: CounterItem[] = [
    { id: "cars", label: "CARS", count: 27412, href: "/motors/cars" },
    {
      id: "number-plates",
      label: "NUMBER PLATES",
      count: 4527,
      href: "/motors/number-plates",
    },
    {
      id: "rental-cars",
      label: "RENTAL CARS",
      count: 1637,
      href: "/motors/rental-cars",
    },
    {
      id: "auto-accessories",
      label: "AUTO ACCESSORIES",
      count: 594,
      href: "/motors/accessories",
    },
    {
      id: "motorcycles",
      label: "MOTORCYCLES",
      count: 401,
      href: "/motors/motorcycles",
    },
    {
      id: "heavy-vehicles",
      label: "HEAVY VEHICLES",
      count: 175,
      href: "/motors/heavy-vehicles",
    },
    { id: "boats", label: "BOATS", count: 133, href: "/motors/boats" },
  ];

  // Sample data for different ad types
  const carAd: CarAd = {
    id: "1",
    category: "car",
    title: "Dodge Charger SXT Plus",
    price: 58000,
    currency: "AED",
    location: "Muhaisnah, Dubai",
    images: ["/images/eastclassified.jpeg"],
    isPremium: true,
    brand: "Dodge",
    model: "Charger",
    year: 2022,
    mileage: 62000,
    transmission: "SXT Plus",
    specs: "American Specs",
    postedDate: "2 days ago",
    description: "HellCat Kit // V6// Full options// warranty// finance0%DP",
    sellerLogo: "/images/eastclassified.jpeg",
  };

  const propertyAd: PropertyAd = {
    id: "2",
    category: "property",
    title: "Spacious 2BR Apartment",
    price: 85000,
    currency: "AED",
    location: "Marina, Dubai",
    images: ["/images/eastclassified.jpeg"],
    propertyType: "Apartment",
    bedrooms: 2,
    bathrooms: 2,
    area: 1200,
    areaUnit: "sq ft",
    furnished: true,
    postedDate: "1 week ago",
    description: "Beautiful 2 bedroom apartment with sea view in Dubai Marina",
  };

  const electronicsAd: ElectronicsAd = {
    id: "3",
    category: "electronics",
    title: "iPhone 15 Pro Max",
    price: 4500,
    currency: "AED",
    location: "Deira, Dubai",
    images: ["/images/eastclassified.jpeg"],
    brand: "Apple",
    model: "iPhone 15 Pro Max",
    condition: "Like New",
    subcategory: "Mobile Phones",
    warranty: true,
    accessories: true,
    postedDate: "3 days ago",
    description:
      "iPhone 15 Pro Max 256GB in excellent condition with all accessories",
  };

  // Create an array of sample ads for demonstration
  const sampleAds = [
    carAd,
    { ...carAd, id: "4", price: 45000, brand: "BMW", model: "X5", year: 2021 },
    propertyAd,
    {
      ...propertyAd,
      id: "5",
      price: 120000,
      propertyType: "Villa",
      bedrooms: 3,
    },
    electronicsAd,
    {
      ...electronicsAd,
      id: "6",
      price: 3200,
      brand: "Samsung",
      model: "Galaxy S24",
    },
    {
      ...carAd,
      id: "7",
      price: 35000,
      brand: "Toyota",
      model: "Camry",
      year: 2020,
      isPremium: false,
    },
    {
      ...propertyAd,
      id: "8",
      price: 65000,
      propertyType: "Studio",
      bedrooms: 0,
      bathrooms: 1,
    },
    {
      ...electronicsAd,
      id: "9",
      price: 2800,
      brand: "MacBook",
      model: "Air M2",
      subcategory: "Laptops",
    },
    {
      ...carAd,
      id: "10",
      price: 28000,
      brand: "Honda",
      model: "Civic",
      year: 2019,
      isPremium: false,
    },
    {
      ...propertyAd,
      id: "11",
      price: 95000,
      propertyType: "Townhouse",
      bedrooms: 3,
      bathrooms: 2,
    },
    {
      ...electronicsAd,
      id: "12",
      price: 1500,
      brand: "Sony",
      model: "WH-1000XM5",
      subcategory: "Audio",
    },
  ];

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <NavBarComponent />
      <CategoryFilterNavBar />
      <MainCategorySearchComponent bgImageUrl={bgImageUrl} />
      <ItemCounterComponent title="Motors Categories" items={defaultItems} />
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {sampleAds.map((ad) => (
              <div key={ad.id} className="w-full">
                <AdsCardComponent ad={ad} />
              </div>
            ))}
          </div>

          <div className="mt-8 flex justify-center">
            <div className="flex items-center gap-4">
              <button className="px-6 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                Previous
              </button>
              <div className="flex items-center gap-2">
                <button className="w-8 h-8 bg-primary text-white rounded-lg">
                  1
                </button>
                <button className="w-8 h-8 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                  2
                </button>
                <button className="w-8 h-8 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                  3
                </button>
                <span className="text-gray-500">...</span>
                <button className="w-8 h-8 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                  10
                </button>
              </div>
              <button className="px-6 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                Next
              </button>
            </div>
          </div>
        </div>
      </main>
      <FooterComponent />
    </div>
  );
}
