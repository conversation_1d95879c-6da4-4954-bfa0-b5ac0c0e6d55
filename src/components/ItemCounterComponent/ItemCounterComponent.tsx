"use client";
import { CounterItem } from "@/model/counterModel";
import React from "react";

interface ItemCounterComponentProps {
  items: CounterItem[];
  title?: string;
  className?: string;
}

export default function ItemCounterComponent({
  items,
  title,
  className = "",
}: ItemCounterComponentProps) {
  const handleItemClick = (item: CounterItem) => {
    if (item.href) {
      window.location.href = item.href;
    }
    console.log(`Clicked on ${item.label} with ${item.count} items`);
  };

  return (
    <div className={`w-full py-2 md:py-2 ${className}`}>
      {title && (
        <div className="max-w-7xl mx-auto px-3 md:px-4 mb-4 md:mb-6">
          <h2 className="text-xl md:text-2xl font-bold text-primary">
            {title}
          </h2>
        </div>
      )}

      <div className="max-w-7xl mx-auto">
        {/* Mobile: Horizontal scroll */}
        <div className="md:hidden overflow-x-auto scrollbar-hide px-3">
          <div className="flex gap-3 pb-2 min-w-max">
            {items.map((item) => (
              <div
                key={item.id}
                onClick={() => handleItemClick(item)}
                className={` 
                  bg-white rounded-lg border border-gray-200 shadow-sm 
                  hover:shadow-md transition-all duration-200 
                  ${item.href ? "cursor-pointer hover:border-primary/30" : ""} 
                  p-3 text-center 
                  w-32 min-w-[128px] flex-shrink-0
                  group 
                  flex flex-col justify-center
                `}
              >
                {/* Count */}
                <div className="text-lg font-bold text-primary mb-1 group-hover:text-primary transition-colors">
                  {item.count.toLocaleString()}
                </div>

                {/* Label */}
                <div className="text-xs font-medium text-gray-600 uppercase tracking-wide leading-tight break-words">
                  {item.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Tablet and Desktop: Grid layout */}
        <div className="hidden md:block px-3 md:px-4">
          <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:flex xl:justify-center gap-3 md:gap-4 lg:gap-6">
            {items.map((item) => (
              <div
                key={item.id}
                onClick={() => handleItemClick(item)}
                className={` 
                  bg-white rounded-lg border border-gray-200 shadow-sm 
                  hover:shadow-md transition-all duration-200 
                  ${item.href ? "cursor-pointer hover:border-primary/30" : ""} 
                  p-4 lg:p-6 text-center 
                  w-full xl:w-40 xl:min-w-[160px]
                  group 
                  flex flex-col justify-center
                `}
              >
                {/* Count */}
                <div className="text-xl md:text-xl lg:text-2xl font-bold text-primary mb-2 group-hover:text-primary transition-colors">
                  {item.count.toLocaleString()}
                </div>

                {/* Label */}
                <div className="text-sm font-medium text-gray-600 uppercase tracking-wide leading-tight break-words">
                  {item.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Add this CSS to your global styles or component styles */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}