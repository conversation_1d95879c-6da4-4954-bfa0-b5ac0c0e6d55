"use client";

import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, Filter, Grid3X3, List } from "lucide-react";

interface CategoryBadge {
  name: string;
  count: number;
}

// Define badges for different categories
const CATEGORY_BADGES = {
  motors: [
    { name: "Mercedes-Benz", count: 4211 },
    { name: "Toyota", count: 2542 },
    { name: "BMW", count: 2121 },
    { name: "Nissan", count: 1980 },
    { name: "Land Rover", count: 1730 },
    { name: "Ford", count: 1195 },
    { name: "Porsche", count: 1120 },
    { name: "Audi", count: 963 },
    { name: "Honda", count: 892 },
    { name: "Hyundai", count: 756 },
    { name: "Lexus", count: 645 },
    { name: "Volkswagen", count: 532 },
    { name: "Chevrolet", count: 478 },
    { name: "Mitsubishi", count: 423 },
    { name: "Jeep", count: 389 },
  ],

  property: [
    { name: "Studio", count: 3245 },
    { name: "1 Bedroom", count: 4892 },
    { name: "2 Bedroom", count: 5234 },
    { name: "3 Bedroom", count: 3456 },
    { name: "4 Bedroom", count: 1892 },
    { name: "Villa", count: 2134 },
    { name: "Apartment", count: 6789 },
    { name: "Townhouse", count: 1567 },
    { name: "Penthouse", count: 234 },
    { name: "Office", count: 892 },
    { name: "Shop", count: 567 },
    { name: "Warehouse", count: 234 },
  ],

  electronics: [
    { name: "Apple", count: 2345 },
    { name: "Samsung", count: 1892 },
    { name: "Sony", count: 1234 },
    { name: "LG", count: 987 },
    { name: "Dell", count: 756 },
    { name: "HP", count: 654 },
    { name: "Canon", count: 543 },
    { name: "Nikon", count: 432 },
    { name: "Huawei", count: 389 },
    { name: "Xiaomi", count: 345 },
    { name: "Lenovo", count: 298 },
    { name: "Asus", count: 234 },
  ],

  jobs: [
    { name: "Full Time", count: 5432 },
    { name: "Part Time", count: 2134 },
    { name: "Contract", count: 1567 },
    { name: "Freelance", count: 892 },
    { name: "Internship", count: 456 },
    { name: "Remote", count: 789 },
    { name: "Dubai", count: 3456 },
    { name: "Abu Dhabi", count: 2134 },
    { name: "Sharjah", count: 1234 },
    { name: "IT & Software", count: 2345 },
    { name: "Sales & Marketing", count: 1892 },
    { name: "Engineering", count: 1567 },
  ],

  furniture: [
    { name: "Sofa", count: 1234 },
    { name: "Bed", count: 987 },
    { name: "Dining Table", count: 756 },
    { name: "Wardrobe", count: 654 },
    { name: "Office Chair", count: 543 },
    { name: "TV Stand", count: 432 },
    { name: "Bookshelf", count: 345 },
    { name: "Coffee Table", count: 298 },
    { name: "Mattress", count: 267 },
    { name: "Dresser", count: 234 },
    { name: "Desk", count: 198 },
    { name: "Cabinet", count: 156 },
  ],

  fashion: [
    { name: "Men's Clothing", count: 2345 },
    { name: "Women's Clothing", count: 3456 },
    { name: "Shoes", count: 1892 },
    { name: "Bags", count: 1234 },
    { name: "Watches", count: 987 },
    { name: "Jewelry", count: 756 },
    { name: "Accessories", count: 654 },
    { name: "Kids Clothing", count: 543 },
    { name: "Sportswear", count: 432 },
    { name: "Formal Wear", count: 345 },
    { name: "Sunglasses", count: 298 },
    { name: "Perfumes", count: 234 },
  ],
};

interface CategoryBadgesProps {
  category: string;
  totalAds?: number;
  location?: string;
  viewMode: "grid" | "list";
  setViewMode: (viewMode: "grid" | "list") => void;
}

export default function CategoryBadges({
  category,
  totalAds = 30920,
  location = "Dubai",
  viewMode,
  setViewMode,
}: CategoryBadgesProps) {
  const [showAll, setShowAll] = useState(false);

  // Get badges for the current category, default to motors if not found
  const badges =
    CATEGORY_BADGES[category as keyof typeof CATEGORY_BADGES] ||
    CATEGORY_BADGES.motors;

  // Show different amounts based on screen size
  const mobileShowCount = 6;
  const desktopShowCount = 8;
  const visibleBadges = showAll ? badges : badges.slice(0, desktopShowCount);
  const hasMoreBadges = badges.length > desktopShowCount;

  // Format category name for display
  const formatCategoryName = (cat: string): string => {
    const categoryNames: { [key: string]: string } = {
      motors: "Cars for sale",
      property: "Property for rent",
      electronics: "Electronics",
      jobs: "Jobs",
      furniture: "Furniture",
      fashion: "Fashion",
    };
    return categoryNames[cat] || cat.charAt(0).toUpperCase() + cat.slice(1);
  };

  const handleBadgeClick = (badgeName: string) => {
    console.log(`Filtering by: ${badgeName}`);
    // Implement filter logic here
  };

  return (
    <div className="bg-white border-b border-gray-200 py-3 sm:py-4">
      <div className="max-w-7xl mx-auto px-3 sm:px-4">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0 mb-3 sm:mb-4">
          <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
            <h2 className="text-lg sm:text-xl font-bold text-gray-900 leading-tight">
              {formatCategoryName(category)}
            </h2>
            <div className="flex items-center gap-2 text-sm sm:text-base">
              <span className="text-gray-500 hidden sm:inline">•</span>
              <span className="text-gray-600 text-sm sm:text-base">
                <span className="font-medium">{totalAds.toLocaleString()}</span>{" "}
                Ads in <span className="font-medium">{location}</span>
              </span>
            </div>
          </div>

          {/* Sort Dropdown - Desktop */}
          <div className="hidden sm:flex items-center gap-3">
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-600">Sort:</span>
              <Button
                variant="ghost"
                className="text-gray-900 hover:text-primary hover:bg-primary/5 font-medium p-1 h-auto"
              >
                Default
                <ChevronDown className="ml-1 h-4 w-4" />
              </Button>
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600 font-medium">
                    View:
                  </span>
                  <div className="flex items-center border border-gray-200 rounded-lg p-1 bg-white">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setViewMode("grid")}
                      className={`px-3 py-1.5 ${
                        viewMode === "grid"
                          ? "bg-primary text-white shadow-sm hover:bg-primary/90"
                          : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                      }`}
                    >
                      <Grid3X3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setViewMode("list")}
                      className={`px-3 py-1.5 ${
                        viewMode === "list"
                          ? "bg-primary text-white shadow-sm hover:bg-primary/90"
                          : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                      }`}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sort Dropdown - Mobile */}
          <div className="flex sm:hidden items-center justify-between">
            <div className="flex items-center gap-2 text-sm">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">Sort:</span>
              <Button
                variant="ghost"
                className="text-gray-900 hover:text-primary hover:bg-primary/5 font-medium p-1 h-auto text-sm"
              >
                Default
                <ChevronDown className="ml-1 h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Badges Container */}
        <div className="space-y-3">
          {/* Mobile: Horizontal Scroll */}
          <div className="sm:hidden">
            <div className="flex gap-2 overflow-x-auto scrollbar-hide pb-1">
              <div className="flex gap-2">
                {badges
                  .slice(0, showAll ? badges.length : mobileShowCount)
                  .map((badge) => (
                    <Badge
                      key={badge.name}
                      variant="outline"
                      className="cursor-pointer bg-white border-gray-200 text-gray-700 hover:border-primary hover:text-primary hover:bg-primary/5 transition-colors duration-200 px-3 py-2 text-xs font-medium whitespace-nowrap flex-shrink-0"
                      onClick={() => handleBadgeClick(badge.name)}
                    >
                      {badge.name} ({badge.count})
                    </Badge>
                  ))}
                {/* Mobile View More/Less */}
                {badges.length > mobileShowCount && (
                  <Button
                    variant="ghost"
                    onClick={() => setShowAll(!showAll)}
                    className="text-primary hover:text-primary hover:bg-primary/5 font-medium text-xs px-3 py-2 h-auto whitespace-nowrap flex-shrink-0"
                  >
                    {showAll ? (
                      <>
                        Less <ChevronUp className="ml-1 h-3 w-3" />
                      </>
                    ) : (
                      <>
                        +{badges.length - mobileShowCount} More{" "}
                        <ChevronDown className="ml-1 h-3 w-3" />
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Desktop: Wrap Layout */}
          <div className="hidden sm:flex flex-wrap items-center gap-2">
            {visibleBadges.map((badge, index) => (
              <Badge
                key={badge.name}
                variant="outline"
                className="cursor-pointer bg-white border-gray-200 text-gray-700 hover:border-primary hover:text-primary hover:bg-primary/5 transition-colors duration-200 px-3 py-1.5 text-sm font-medium"
                onClick={() => handleBadgeClick(badge.name)}
              >
                {badge.name} ({badge.count})
              </Badge>
            ))}

            {/* Desktop View More/Less Toggle */}
            {hasMoreBadges && (
              <Button
                variant="ghost"
                onClick={() => setShowAll(!showAll)}
                className="text-primary hover:text-primary hover:bg-primary/5 font-medium text-sm px-3 py-1.5 h-auto"
              >
                {showAll ? (
                  <>
                    View Less <ChevronUp className="ml-1 h-4 w-4" />
                  </>
                ) : (
                  <>
                    View More <ChevronDown className="ml-1 h-4 w-4" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>

      <style jsx global>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}
