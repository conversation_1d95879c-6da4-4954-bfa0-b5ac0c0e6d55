"use client";

import { useState } from "react";
import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Image from "next/image";

export default function HomeSearchComponent() {
  const [activeCategory, setActiveCategory] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("");

  const categories = [
    { name: "All", color: "bg-primary" },
    { name: "Motors", color: "bg-white/20" },
    { name: "Property", color: "bg-white/20" },
    { name: "Job<PERSON>", color: "bg-white/20" },
    { name: "Classifieds", color: "bg-white/20" },
    { name: "Community", color: "bg-white/20" },
  ];

  const handleSearch = () => {
    console.log("Searching:", {
      category: activeCategory,
      query: searchQuery,
      location: selectedLocation,
    });
    // Add your search logic here
  };

  return (
    <div className="w-full px-4 md:px-6 lg:px-8 py-6 md:py-8">
      <div className="relative min-h-[350px] md:min-h-[400px] lg:min-h-[450px] overflow-hidden rounded-xl md:rounded-2xl max-w-7xl mx-auto">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 rounded-xl md:rounded-2xl">
          <Image
            src="/images/homehero.jpeg"
            alt="Hero Background"
            fill
            className="object-cover rounded-xl md:rounded-2xl"
            priority
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 1200px"
          />
          {/* Dark overlay for better text readability */}
          <div className="absolute inset-0 bg-black/40 rounded-xl md:rounded-2xl"></div>
        </div>

        <div className="relative z-10 max-w-5xl mx-auto px-4 md:px-6 py-8 md:py-12 lg:py-16">
          {/* Hero Text */}
          <div className="text-center mb-6 md:mb-8">
            <h1 className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-white mb-2 md:mb-3 leading-tight px-2">
              The best place to buy, sell & find anything in UAE
            </h1>
            <p className="text-xs sm:text-sm md:text-base text-white/90 max-w-2xl mx-auto px-4">
              Discover amazing deals on cars, homes, jobs and more from people
              around you in UAE
            </p>
          </div>

          {/* Category Tabs */}
          <div className="flex flex-wrap justify-center gap-2 md:gap-8 mb-6 md:mb-8 px-2">
            {categories.map((category) => (
              <button
                key={category.name}
                onClick={() => setActiveCategory(category.name)}
                className={`px-3 md:px-4 py-2 md:py-2.5 rounded-full text-xs md:text-sm font-semibold transition-all duration-200 ${
                  activeCategory === category.name
                    ? "bg-white text-primary shadow-lg scale-105"
                    : "bg-white/20 text-white hover:bg-white/30 hover:scale-105"
                }`}
              >
                {category.name}
                {category.name === "Motors" && (
                  <span className="ml-1.5 bg-white text-primary text-xs px-1.5 py-0.5 rounded-full">
                    NEW
                  </span>
                )}
                {category.name === "Property" && (
                  <span className="ml-1.5 bg-white text-primary text-xs px-1.5 py-0.5 rounded-full">
                    HOT
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Search Box */}
          <div className="bg-white rounded-lg md:rounded-xl shadow-lg p-3 md:p-4 max-w-3xl mx-auto mb-8 md:mb-12">
            <div className="flex flex-col sm:flex-row gap-2 md:gap-3">
              {/* Search Input */}
              <div className="flex-1 relative">
                <Input
                  type="text"
                  placeholder={`What are you looking for${
                    activeCategory !== "All" ? ` in ${activeCategory}` : ""
                  }?`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-10 md:h-12 pl-4 pr-10 border border-gray-200 rounded-md md:rounded-lg bg-gray-50 hover:border-primary/30 focus:border-primary transition-colors text-sm placeholder:text-gray-400"
                  onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 md:h-5 md:w-5 text-gray-400" />
              </div>

              {/* Search Button */}
              <Button
                onClick={handleSearch}
                className="h-10 md:h-12 px-6 md:px-8 bg-primary hover:bg-primary/90 text-white rounded-md md:rounded-lg font-medium text-sm md:text-base transition-all duration-200 shadow-md hover:shadow-lg"
              >
                Search
              </Button>
            </div>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8 text-center px-2">
            <div className="text-white">
              <div className="text-xl md:text-2xl lg:text-3xl font-bold mb-1">1M+</div>
              <div className="text-white/90 text-xs md:text-sm lg:text-base">
                Active Listings
              </div>
            </div>
            <div className="text-white">
              <div className="text-xl md:text-2xl lg:text-3xl font-bold mb-1">500K+</div>
              <div className="text-white/90 text-xs md:text-sm lg:text-base">Happy Users</div>
            </div>
            <div className="text-white">
              <div className="text-xl md:text-2xl lg:text-3xl font-bold mb-1">50+</div>
              <div className="text-white/90 text-xs md:text-sm lg:text-base">
                Cities Covered
              </div>
            </div>
            <div className="text-white">
              <div className="text-xl md:text-2xl lg:text-3xl font-bold mb-1">24/7</div>
              <div className="text-white/90 text-xs md:text-sm lg:text-base">Support</div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-6 md:top-10 left-6 md:left-10 w-16 md:w-20 h-16 md:h-20 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-6 md:bottom-10 right-6 md:right-10 w-24 md:w-32 h-24 md:h-32 bg-white/5 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/4 w-12 md:w-16 h-12 md:h-16 bg-white/5 rounded-full blur-lg"></div>
      </div>
    </div>
  );
}