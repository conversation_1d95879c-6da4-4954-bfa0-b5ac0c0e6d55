import React from "react";
import { <PERSON>, <PERSON>Off, Mail, Lock, User, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useRouter } from "next/navigation";

interface SignUpComponentProps extends React.HTMLAttributes<"div"> {
  isLoading: boolean;
  formData: {
    email: string;
    password: string;
  };
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSubmit: () => void;
  showPassword: boolean;
  showConfirmPassword: boolean;
  setShowPassword: (show: boolean) => void;
  setShowConfirmPassword: (show: boolean) => void;
}

export default function LoginComponent({
  isLoading,
  formData,
  handleInputChange,
  handleSubmit,
  showConfirmPassword,
  setShowConfirmPassword,
}: SignUpComponentProps) {
  const router = useRouter();
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-4">
          <h1 className="text-3xl font-bold mb-1 text-primary">
            Welcome to EastClassified
          </h1>
          <p className="text-sm text-muted-foreground">Login to your account</p>
        </div>

        {/* Card */}
        <Card className="shadow-lg border bg-white border-gray-100">
          <CardHeader className="space-y-1 pb-4 px-4">
            <CardTitle className="text-lg text-center text-primary">
              Login
            </CardTitle>
            <CardDescription className="text-center text-xs text-muted-foreground">
              Enter your details
            </CardDescription>
          </CardHeader>

          <CardContent className="px-4 pb-4">
            <div className="space-y-3">
              {/* Email */}
              <div className="space-y-1">
                <Label
                  htmlFor="email"
                  className="text-xs font-medium text-primary"
                >
                  Email
                </Label>
                <div className="relative">
                  <Mail className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    className="pl-8 h-9 text-sm"
                    placeholder="Your email"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Password */}
              <div className="space-y-1">
                <Label
                  htmlFor="password"
                  className="text-xs font-medium text-primary"
                >
                  Password
                </Label>
                <div className="relative">
                  <Lock className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-primary" />
                  <Input
                    id="password"
                    name="password"
                    type={showConfirmPassword ? "text" : "password"}
                    required
                    className="pl-8 pr-9 h-9 text-sm text-black placeholder:text-muted-foreground"
                    placeholder="Password"
                    value={formData.password}
                    onChange={handleInputChange}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-2 hover:text-primary hover:bg-transparent text-primary/50"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-3.5 w-3.5" />
                    ) : (
                      <Eye className="h-3.5 w-3.5" />
                    )}
                  </Button>
                </div>
              </div>
              {/* Submit */}
              <Button
                onClick={handleSubmit}
                disabled={isLoading}
                className="w-full h-9 text-sm font-medium bg-primary text-white-foreground mt-3"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-3.5 w-3.5 border-b-2 border-primary-foreground mr-2"></div>
                    Loading...
                  </>
                ) : (
                  <>
                    Login <ArrowRight className="ml-1 h-3.5 w-3.5" />
                  </>
                )}
              </Button>
            </div>

            {/* Sign Up */}
            <div className="mt-3 text-center">
              <p className="text-xs text-muted-foreground">
                Dont have an account? {" "}
                <span onClick={() => router.push("/signup")}  className="font-medium text-primary cursor-pointer">
                  Sign up
                </span>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-3 text-center">
          <p className="text-[10px] text-muted-foreground">
            By signing up, you agree to our Privacy Policy & Terms
          </p>
        </div>
      </div>
    </div>
  );
}
