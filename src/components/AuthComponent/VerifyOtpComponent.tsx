import React from "react";
import { <PERSON>, EyeOff, Lock, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface VerifyOtpComponentProps extends React.HTMLAttributes<"div"> {
  isLoading: boolean;
  formData: {
    email: string;
    otp: string;
    userOtp: string;
  };
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSubmit: () => void;
  showOtp: boolean;
  showUserOtp: boolean;
  setShowOtp: (show: boolean) => void;
  setShowUserOtp: (show: boolean) => void;
}

export default function VerifyOtpComponent({
  isLoading,
  formData,
  handleInputChange,
  handleSubmit,
  showUserOtp,
  setShowUserOtp,
}: VerifyOtpComponentProps) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-4">
          <h1 className="text-3xl font-bold mb-1 text-primary">
            Verify Your Account
          </h1>
          <p className="text-sm text-muted-foreground">Enter OTP to continue</p>
        </div>

        {/* Card */}
        <Card className="shadow-lg border bg-white border-gray-100">
          <CardHeader className="space-y-1 pb-4 px-4">
            <CardTitle className="text-lg text-center text-primary">
              OTP Verification
            </CardTitle>
            <CardDescription className="text-center text-xs text-muted-foreground">
              We have sent a verification code to your email
            </CardDescription>
          </CardHeader>

          <CardContent className="px-4 pb-4">
            <div className="space-y-4">
              {/* User OTP */}
              <div className="space-y-1">
                <Label
                  htmlFor="userOtp"
                  className="text-xs font-medium text-primary"
                >
                  OTP Code
                </Label>
                <div className="relative">
                  <Lock className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-primary" />
                  <Input
                    id="userOtp"
                    name="userOtp"
                    type={showUserOtp ? "text" : "password"}
                    required
                    className="pl-8 pr-9 h-9 text-sm text-black placeholder:text-muted-foreground"
                    placeholder="Enter OTP code"
                    value={formData.userOtp}
                    onChange={handleInputChange}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-2 text-primary"
                    onClick={() => setShowUserOtp(!showUserOtp)}
                  >
                    {showUserOtp ? (
                      <EyeOff className="h-3.5 w-3.5" />
                    ) : (
                      <Eye className="h-3.5 w-3.5" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Submit */}
              <Button
                onClick={handleSubmit}
                disabled={isLoading}
                className="w-full h-9 text-sm font-medium bg-primary text-white"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-3.5 w-3.5 border-b-2 border-primary-foreground mr-2"></div>
                    Verifying...
                  </>
                ) : (
                  <>
                    Verify OTP <ArrowRight className="ml-1 h-3.5 w-3.5" />
                  </>
                )}
              </Button>

              {/* Resend OTP */}
              <div className="text-center pt-2">
                <p className="text-xs text-muted-foreground">
                  Did not receive the code?
                  <span className="font-medium underline text-primary cursor-pointer">
                    Resend OTP
                  </span>
                </p>
              </div>
            </div>

            {/* Sign In Link */}
            <div className="mt-3 text-center">
              <p className="text-xs text-muted-foreground">
                Already have an account?
                <span className="font-medium underline text-primary cursor-pointer">
                  Sign in
                </span>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-3 text-center">
          <p className="text-[10px] text-muted-foreground">
            Please check your email for the verification code
          </p>
        </div>
      </div>
    </div>
  );
}
