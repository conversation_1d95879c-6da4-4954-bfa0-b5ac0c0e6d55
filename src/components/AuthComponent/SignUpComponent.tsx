import React from "react";
import { <PERSON>, EyeOff, Mail, Lock, User, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useRouter } from "next/navigation";

interface SignUpComponentProps extends React.HTMLAttributes<"div"> {
  isLoading: boolean;
  formData: {
    fullName: string;
    email: string;
    password: string;
    confirmPassword: string;
  };
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSubmit: () => void;
  showPassword: boolean;
  showConfirmPassword: boolean;
  setShowPassword: (show: boolean) => void;
  setShowConfirmPassword: (show: boolean) => void;
}

export default function SignUpComponent({
  isLoading,
  formData,
  handleInputChange,
  handleSubmit,
  showPassword,
  showConfirmPassword,
  setShowPassword,
  setShowConfirmPassword,
}: SignUpComponentProps) {
  const router = useRouter();
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-4">
          <h1 className="text-3xl font-bold mb-1 text-primary">
            Create Your Account
          </h1>
          <p className="text-sm text-muted-foreground">
            Join EastClassified Today
          </p>
        </div>

        {/* Card */}
        <Card className="shadow-lg border bg-white border-gray-100">
          <CardHeader className="space-y-1 pb-4 px-4">
            <CardTitle className="text-lg text-center text-primary">
              Sign Up
            </CardTitle>
            <CardDescription className="text-center text-xs text-muted-foreground">
              Enter your details
            </CardDescription>
          </CardHeader>

          <CardContent className="px-4 pb-4">
            <div className="space-y-3">
              {/* Full Name */}
              <div className="space-y-1">
                <Label
                  htmlFor="fullName"
                  className="text-xs font-medium text-primary"
                >
                  Full Name
                </Label>
                <div className="relative">
                  <User className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
                  <Input
                    id="fullName"
                    name="fullName"
                    type="text"
                    required
                    className="pl-8 h-9 text-sm"
                    placeholder="Your full name"
                    value={formData.fullName}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Email */}
              <div className="space-y-1">
                <Label
                  htmlFor="email"
                  className="text-xs font-medium text-primary"
                >
                  Email
                </Label>
                <div className="relative">
                  <Mail className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    className="pl-8 h-9 text-sm"
                    placeholder="Your email"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Password */}
              <div className="space-y-1">
                <Label
                  htmlFor="password"
                  className="text-xs font-medium text-primary"
                >
                  Password
                </Label>
                <div className="relative">
                  <Lock className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    required
                    className="pl-8 pr-9 h-9 text-sm text-black placeholder:text-muted-foreground"
                    placeholder="Password"
                    value={formData.password}
                    onChange={handleInputChange}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-2 hover:text-primary hover:bg-transparent text-primary/50"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-3.5 w-3.5" />
                    ) : (
                      <Eye className="h-3.5 w-3.5" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Confirm Password */}
              <div className="space-y-1">
                <Label
                  htmlFor="confirmPassword"
                  className="text-xs font-medium text-primary"
                >
                  Confirm Password
                </Label>
                <div className="relative">
                  <Lock className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    required
                    className="pl-8 pr-9 h-9 text-sm text-black placeholder:text-muted-foreground"
                    placeholder="Confirm password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-2 hover:text-primary hover:bg-transparent text-primary/50"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-3.5 w-3.5" />
                    ) : (
                      <Eye className="h-3.5 w-3.5" />
                    )}
                  </Button>
                </div>
              </div>
              {/* Submit */}
              <Button
                onClick={handleSubmit}
                disabled={isLoading}
                className="w-full h-9 text-sm font-medium bg-primary text-white-foreground mt-3"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-3.5 w-3.5 border-b-2 border-primary-foreground mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    Create Account <ArrowRight className="ml-1 h-3.5 w-3.5" />
                  </>
                )}
              </Button>
            </div>

            {/* Sign In */}
            <div className="mt-3 text-center">
              <p className="text-xs text-muted-foreground">
                Already have an account?{" "}
                <span onClick={() => router.push("/login")} className="font-medium underline text-primary cursor-pointer">
                  Sign in
                </span>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-3 text-center">
          <p className="text-[10px] text-muted-foreground">
            By signing up, you agree to our Privacy Policy & Terms
          </p>
        </div>
      </div>
    </div>
  );
}
