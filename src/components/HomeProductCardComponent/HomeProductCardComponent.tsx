"use client";

import { Heart, MapPin, Clock, Verified, Eye } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import Image from "next/image";

interface ProductCardProps {
  id: string;
  title: string;
  price: number;
  currency?: "AED";
  location: string;
  images: string[];
  featured?: boolean;
  verified?: boolean;
  urgent?: boolean;
  condition?: "new" | "used" | "excellent" | "good" | "fair";
  postedTime: string;
  views: number;
  isFavorite?: boolean;
  onToggleFavorite?: () => void;

  // Category specific props
  category:
    | "cars"
    | "property"
    | "jobs"
    | "electronics"
    | "furniture"
    | "fashion"
    | "services";

  // Property specific
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  areaUnit?: "sqft" | "sqm";

  // Cars specific
  year?: number;
  brand?: string;
  mileage?: number;

  // Jobs specific
  company?: string;
  jobType?: "full-time" | "part-time" | "contract";
  salary?: string;
}

export default function ProductCard({
  id,
  title,
  price,
  currency = "AED",
  location,
  images,
  featured = false,
  verified = false,
  urgent = false,
  condition,
  postedTime,
  views,
  isFavorite = false,
  onToggleFavorite,
  category,
  bedrooms,
  bathrooms,
  area,
  areaUnit,
  year,
  brand,
  mileage,
  company,
  jobType,
  salary,
}: ProductCardProps) {
  const formatPrice = (price: number) => {
    if (price >= 1000000) {
      return `${(price / 1000000).toFixed(1)}M`;
    } else if (price >= 1000) {
      return `${(price / 1000).toFixed(0)}K`;
    }
    return price.toLocaleString();
  };

  const renderCategoryDetails = () => {
    switch (category) {
      case "property":
        return (
          <div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
            {bedrooms && <span>{bedrooms} bed</span>}
            {bathrooms && <span>{bathrooms} bath</span>}
            {area && (
              <span>
                {area.toLocaleString()} {areaUnit}
              </span>
            )}
          </div>
        );

      case "cars":
        return (
          <div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
            {year && <span>{year}</span>}
            {brand && <span>{brand}</span>}
            {mileage && <span>{mileage.toLocaleString()} km</span>}
          </div>
        );

      case "jobs":
        return (
          <div className="mb-1">
            {company && (
              <p className="text-xs font-medium text-gray-700 mb-0.5">
                {company}
              </p>
            )}
            {jobType && (
              <span className="text-xs text-gray-500 capitalize">
                {jobType}
              </span>
            )}
          </div>
        );

      default:
        return (
          condition && (
            <div className="text-xs text-gray-500 mb-1 capitalize">
              <span>{condition}</span>
            </div>
          )
        );
    }
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 border-1 border-gray-200 shadow-sm overflow-hidden bg-white h-fit">
      <Link href={`/ad/${id}`}>
        <div className="relative">
          {/* Main Image */}
          <div className="relative h-36 md:h-40 overflow-hidden bg-gray-100">
            <Image
              src={images[0] || "/api/placeholder/300/200"}
              alt={title}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              priority={featured}
            />

            {/* Image Counter */}
            {images.length > 1 && (
              <div className="absolute bottom-1.5 right-1.5 bg-black/70 text-white px-1.5 py-0.5 rounded text-xs">
                +{images.length - 1}
              </div>
            )}
          </div>

          {/* Badges */}
          <div className="absolute top-1 left-1 flex gap-0.5 flex-wrap">
            {urgent && (
              <Badge className="bg-red-500 text-white px-1 py-0.5 text-[10px] font-medium h-auto leading-none">
                URGENT
              </Badge>
            )}
            {featured && (
              <Badge className="bg-primary text-white px-1 py-0.5 text-[10px] font-medium h-auto leading-none">
                FEATURED
              </Badge>
            )}
            {verified && (
              <Badge className="bg-green-500 text-white px-1 py-0.5 text-[10px] font-medium flex items-center gap-0.5 h-auto leading-none">
                <Verified className="h-2 w-2" />
                VERIFIED
              </Badge>
            )}
          </div>

          {/* Favorite Button */}
          {onToggleFavorite && (
            <button
              onClick={(e) => {
                e.preventDefault();
                onToggleFavorite();
              }}
              className="absolute top-1.5 right-1.5 p-1.5 bg-white/90 rounded-full hover:bg-white transition-colors shadow-sm"
            >
              <Heart
                className={`h-3.5 w-3.5 ${
                  isFavorite ? "text-red-500 fill-red-500" : "text-gray-600"
                }`}
              />
            </button>
          )}
        </div>

        <CardContent className="p-3">
          {/* Title */}
          <h3 className="font-medium text-gray-900 text-xs leading-4 group-hover:text-primary transition-colors line-clamp-2 mb-1.5 min-h-[2rem]">
            {title}
          </h3>

          {/* Price */}
          <div className="flex items-baseline gap-1 mb-2">
            <span className="text-base font-bold text-primary">
              {category === "jobs" && salary
                ? salary
                : `${currency} ${formatPrice(price)}`}
            </span>
            {category === "property" && (
              <span className="text-xs text-gray-500">/month</span>
            )}
          </div>

          {/* Category Specific Details */}
          {renderCategoryDetails()}

          {/* Location and Time */}
          <div className="flex items-center justify-between text-xs text-gray-400 mt-2">
            <div className="flex items-center gap-1 flex-1 min-w-0">
              <MapPin className="h-3 w-3 flex-shrink-0" />
              <span className="truncate text-xs">{location}</span>
            </div>
            <div className="flex items-center gap-2 ml-2 flex-shrink-0">
              <div className="flex items-center gap-0.5">
                <Eye className="h-3 w-3" />
                <span className="text-xs">{views}</span>
              </div>
              <div className="flex items-center gap-0.5">
                <Clock className="h-3 w-3" />
                <span className="text-xs">{postedTime}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Link>
    </Card>
  );
}