import { ChevronRight } from "lucide-react";
import { But<PERSON> } from "../ui/button";

interface HomeProductHeadingComponentProps extends React.HTMLAttributes<"div"> {
  title: string;
}

export default function HomeProductHeadingComponent({
  title,
}: HomeProductHeadingComponentProps) {
  return (
    <div className="flex items-center justify-between mb-2 mr-4">
      <h2 className="text-xl font-bold text-primary">
        {title}
      </h2>
      <Button
        variant="outline"
        className="text-primary border-primary hover:bg-primary hover:text-white text-xs"
      >
        View All
        <ChevronRight className="ml-2 h-4 w-4" />
      </Button>
    </div>
  );
}
