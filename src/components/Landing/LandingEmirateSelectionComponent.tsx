import { <PERSON>R<PERSON>, MapPin } from "lucide-react";
import { Card, CardContent } from "../ui/card";
import { Button } from "../ui/button";

interface LandingEmirateSelectionComponentProps
  extends React.ComponentProps<"div"> {
  emirates: {
    name: string;
    description: string;
  }[];
  handleEmirateClick: (emirate: string) => void;
}

export default function LandingEmirateSelectionComponent({
  emirates,
  handleEmirateClick,
}: LandingEmirateSelectionComponentProps) {
  return (
    <section className="py-8 md:py-16 px-6 sm:px-4 md:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-primary mb-3 md:mb-4">
            Choose Your Emirate
          </h2>
          <p className="text-gray-600 text-sm sm:text-base md:text-lg max-w-2xl mx-auto px-2">
            Select your location to start browsing thousands of listings in your
            area
          </p>
        </div>

        {/* Emirates Grid - Fully Responsive */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-w-sm sm:max-w-none mx-auto">
          {emirates.slice(0, 7).map((emirate, idx) => (
            <Card
              key={idx}
              className="group cursor-pointer border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-white overflow-hidden hover:border-primary/50 active:scale-95 h-auto"
              onClick={() => handleEmirateClick(emirate.name)}
            >
              <CardContent className="p-0">
                {/* Header with Icon */}
                <div className="bg-gradient-to-br from-primary/10 to-primary/5 p-3 sm:p-4 border-b border-gray-100">
                  <div className="flex items-center text-primary">
                    <MapPin className="w-4 h-4 sm:w-4 sm:h-4 mr-2 flex-shrink-0" />
                    <h3 className="text-sm sm:text-base font-semibold truncate">
                      {emirate.name}
                    </h3>
                  </div>
                </div>

                {/* Content */}
                <div className="p-3 sm:p-4">
                  <p className="text-gray-600 text-xs sm:text-sm leading-relaxed mb-3 sm:mb-4 line-clamp-2">
                    {emirate.description}
                  </p>
                  <Button className="w-full bg-white hover:bg-primary border border-primary text-primary hover:text-white rounded-lg h-8 sm:h-9 text-xs sm:text-sm font-medium transition-all duration-200 group touch-manipulation">
                    <span>Explore</span>
                    <ArrowRight className="w-3 h-3 ml-1 sm:ml-2 group-hover:translate-x-1 transition-transform flex-shrink-0" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}