import { MapPin, Smartphone, PlusCircle, Shield } from "lucide-react";

export default function LandingFeatureSectionComponents() {
  return (
    <section className="bg-gray-50 py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-primary mb-4">
            Why Choose East Classifieds?
          </h3>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Join thousands of satisfied users who trust our platform for safe and easy trading
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-primary" />
            </div>
            <h4 className="text-xl font-semibold text-primary mb-2">
              Trusted Platform
            </h4>
            <p className="text-gray-600">
              Secure and reliable marketplace with verified users and safe transactions
            </p>
          </div>

          <div className="text-center bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="w-8 h-8 text-primary" />
            </div>
            <h4 className="text-xl font-semibold text-primary mb-2">
              UAE Wide
            </h4>
            <p className="text-gray-600">
              Access to all Emirates in one convenient platform with local support
            </p>
          </div>

          <div className="text-center bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Smartphone className="w-8 h-8 text-primary" />
            </div>
            <h4 className="text-xl font-semibold text-primary mb-2">
              Easy to Use
            </h4>
            <p className="text-gray-600">
              Simple and intuitive interface designed for everyone, mobile-friendly
            </p>
          </div>

          <div className="text-center bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <PlusCircle className="w-8 h-8 text-primary" />
            </div>
            <h4 className="text-xl font-semibold text-primary mb-2">
              Post Free Ads
            </h4>
            <p className="text-gray-600">
              Post unlimited ads for free and reach thousands of buyers
            </p>
          </div>
        </div>

        {/* Additional Features Row */}
        {/* <div className="grid md:grid-cols-3 gap-8 mt-12">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">24/7</div>
            <p className="text-gray-600 text-sm">Customer Support</p>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">Free</div>
            <p className="text-gray-600 text-sm">Listing Your Ads</p>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">1M+</div>
            <p className="text-gray-600 text-sm">Active Users</p>
          </div>
        </div> */}
      </div>
    </section>
  );
}