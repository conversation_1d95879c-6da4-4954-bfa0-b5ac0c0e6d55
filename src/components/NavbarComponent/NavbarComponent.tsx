"use client";

import { useState, useEffect } from "react";
import {
  Bell,
  Search,
  Heart,
  MessageCircle,
  Plus,
  ChevronDown,
  User,
  Menu,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function NavBarComponent() {
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
  }, []);

  return (
    <nav className="bg-white border-b border-gray-200">
      {/* Main Navbar */}
      <div className="px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          {/* Logo and Location */}
          <div className="flex items-center gap-4">
            {/* Logo */}
            <div className="flex items-center gap-2">
              <Image
                src="/images/eastclassified.jpeg"
                alt="East Classifieds Logo"
                width={65}
                height={65}
                className="rounded-l-3xl"
              />
              <h1 className="text-md md:text-xl font-bold text-primary">
                East Classifieds
              </h1>
            </div>

            {/* Location Dropdown - Hidden on mobile */}
            <div className="hidden md:block">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="text-primary hover:bg-primary/5 hover:text-primary flex items-center gap-2"
                  >
                    UAE <ChevronDown className="ml-1 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-white text-primary border-white">
                  <DropdownMenuItem>Uae</DropdownMenuItem>
                  <DropdownMenuItem>Dubai</DropdownMenuItem>
                  <DropdownMenuItem>Abu Dhabi</DropdownMenuItem>
                  <DropdownMenuItem>Sharjah</DropdownMenuItem>
                  <DropdownMenuItem>Ajman</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Desktop Navigation Items */}
          {/* <div className="hidden lg:flex items-center gap-6">
            <Button
              variant="ghost"
              className="text-primary hover:bg-primary/5 hover:text-primary flex items-center gap-2"
            >
              <Bell className="h-4 w-4" />
              Notifications
            </Button>

            <Button
              variant="ghost"
              className="text-primary hover:bg-primary/5 hover:text-primary flex items-center gap-2"
            >
              <Search className="h-4 w-4" />
              My Searches
            </Button>

            <Button
              variant="ghost"
              className="text-primary hover:bg-primary/5 hover:text-primary flex items-center gap-2"
            >
              <Heart className="h-4 w-4" />
              Favorites
            </Button>

            <Button
              variant="ghost"
              className="text-primary hover:bg-primary/5 hover:text-primary flex items-center gap-2"
            >
              <MessageCircle className="h-4 w-4" />
              Chats
            </Button>

            <Button
              variant="ghost"
              className="text-primary hover:bg-primary/5 hover:text-primary flex items-center gap-2"
            >
              My Ads
            </Button>
          </div> */}

          {/* Right Side - User and CTA */}
          <div className="flex items-center gap-3">
            {user === null ? (
              <Button
                onClick={() => router.push("/login")}
                variant={"outline"}
                className=" text-primary flex items-center gap-2 px-3 md:px-4 py-2 text-xs md:text-base hover:bg-white hover:text-primary"
              >
                <User className="h-4 w-4" />
                <span className="hidden sm:inline text-xs">Login</span>
              </Button>
            ) : (
              <div className="hidden md:block">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="text-primary hover:bg-primary/5 hover:text-primary flex items-center gap-2"
                    >
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-white" />
                      </div>
                      <span className="hidden lg:inline">
                        {user?.name ?? ""}
                      </span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    className="bg-white text-primary border border-gray-200 shadow-lg w-40 text-xs"
                  >
                    <DropdownMenuItem className="hover:bg-primary hover:text-white cursor-pointer text-xs">
                      Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-primary hover:text-white cursor-pointer text-xs">
                      Notifications
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-primary hover:text-white cursor-pointer text-xs">
                      My Searches
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-primary hover:text-white cursor-pointer text-xs">
                      My Ads
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-primary hover:text-white cursor-pointer text-xs">
                      My Favourites
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-primary hover:text-white cursor-pointer text-xs">
                      My Ads
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-primary hover:text-white cursor-pointer text-xs">
                      Sign Out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
            <Button
              variant="ghost"
              className="text-primary hover:bg-primary/5 hover:text-primary flex items-center gap-2"
            >
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <Plus className="h-4 w-4 text-white" />
              </div>
              <span className="hidden lg:inline">Place Your Ad</span>
            </Button>
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                className="text-primary hover:text-primary hover:bg-primary/5 p-2"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? (
                  <X className="h-5 w-5" />
                ) : (
                  <Menu className="h-5 w-5" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-200">
          <div className="px-4 py-3 space-y-3">
            {/* Location Selector for Mobile */}
            <div className="pb-3 border-b border-gray-100">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-primary hover:bg-primary/10"
                  >
                    📍 Dubai <ChevronDown className="ml-auto h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  <DropdownMenuItem>Dubai</DropdownMenuItem>
                  <DropdownMenuItem>Abu Dhabi</DropdownMenuItem>
                  <DropdownMenuItem>Sharjah</DropdownMenuItem>
                  <DropdownMenuItem>Ajman</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* User Profile for Mobile */}
            <div className="pb-3 border-b border-gray-100">
              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:bg-primary/10"
              >
                <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center mr-3">
                  <User className="h-3 w-3 text-white" />
                </div>
                {user?.name ?? "Guest"}
              </Button>
            </div>

            {/* Navigation Items */}
            <div className="space-y-1">
              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:bg-primary/10"
              >
                <Bell className="h-4 w-4 mr-3" />
                Notifications
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:bg-primary/10"
              >
                <Search className="h-4 w-4 mr-3" />
                My Searches
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:bg-primary/10"
              >
                <Heart className="h-4 w-4 mr-3" />
                Favorites
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:bg-primary/10"
              >
                <MessageCircle className="h-4 w-4 mr-3" />
                Chats
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:bg-primary/10"
              >
                My Ads
              </Button>
            </div>

            {/* User Menu Items */}
            <div className="pt-3 border-t border-gray-100 space-y-1">
              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:bg-primary/10"
              >
                Profile
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:bg-primary/10"
              >
                Settings
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:bg-primary/10"
              >
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
