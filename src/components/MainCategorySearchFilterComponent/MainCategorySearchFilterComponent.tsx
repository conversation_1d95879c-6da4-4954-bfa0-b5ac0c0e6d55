"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface MainCategorySearchComponentProps {
  onSearch?: (query: string) => void;
  bgImageUrl?: string;
}

export default function MainCategorySearchComponent({
  onSearch,
  bgImageUrl,
}: MainCategorySearchComponentProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = () => {
    onSearch?.(searchQuery);
    console.log("Searching for:", searchQuery);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <div className="w-full px-4 md:px-6 lg:px-8 py-6 md:py-8">
      <div className="relative w-full max-w-7xl mx-auto py-12 md:py-16 lg:py-20 flex flex-col items-center rounded-xl md:rounded-2xl overflow-hidden">
        <div className="absolute inset-0 rounded-xl md:rounded-2xl overflow-hidden">
          <Image
            src={bgImageUrl ?? "/images/moters.jpeg"}
            alt="Category Background"
            fill
            priority
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 1200px"
            className="object-cover w-full h-full"
          />

          <div className="absolute inset-0 bg-black/40 md:bg-black/30" />
        </div>

        <div className="relative z-10 w-full max-w-4xl mx-auto px-4 md:px-6 text-center flex flex-col items-center">
          {/* Main Heading */}
          <h1 className="text-xl sm:text-md md:text-lg lg:text-xl xl:text-3xl font-bold text-white mb-6 md:mb-8 leading-tight px-2">
            Everyone is on East Classifieds
          </h1>

          <div className="w-full max-w-2xl bg-white rounded-lg md:rounded-xl shadow-lg md:shadow-xl p-2 md:p-1 md:pr-2 flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-2">
            <div className="relative flex-1">
              <Input
                type="text"
                placeholder="Search for motors"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="border-0 focus:ring-0 focus:border-0 text-sm md:text-base lg:text-lg h-10 md:h-12 lg:h-14 pl-4 pr-4 bg-transparent placeholder:text-gray-400 rounded-md"
              />
            </div>

            <Button
              onClick={handleSearch}
              className="bg-primary hover:bg-primary/90 text-white px-4 md:px-6 h-10 md:h-12 lg:h-14 rounded-md md:rounded-lg font-medium text-sm md:text-base transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center gap-2 w-full sm:w-auto"
            >
              <Search className="h-4 w-4 md:h-5 md:w-5" />
              <span className="sm:hidden md:inline">Search</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
