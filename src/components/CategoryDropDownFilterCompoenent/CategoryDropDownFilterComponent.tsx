"use client";

import React, { useState, useEffect } from "react";
import { ChevronDown, Search, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

const CATEGORY_CONFIGS = {
  motors: {
    name: "Motors",
    icon: "🚗",
    filters: [
      {
        id: "location",
        label: "Location",
        type: "select",
        placeholder: "All UAE",
        options: [
          "Dubai",
          "Abu Dhabi",
          "Sharjah",
          "Ajman",
          "Ras Al Khaimah",
          "Fujairah",
          "Umm Al Quwain",
        ],
      },
      {
        id: "type",
        label: "Type",
        type: "select",
        placeholder: "All Types",
        options: [
          "Cars",
          "Motorcycles",
          "Boats",
          "Heavy Vehicles",
          "Spare Parts",
          "Number Plates",
        ],
      },
      {
        id: "make",
        label: "Make & Model",
        type: "search",
        placeholder: "Search Make, Model...",
        options: [
          "Toyota",
          "BMW",
          "Mercedes-Benz",
          "Audi",
          "Honda",
          "Nissan",
          "Hyundai",
          "Ford",
          "Volkswagen",
          "Lexus",
        ],
      },
      {
        id: "price",
        label: "Price",
        type: "select",
        placeholder: "Any Price",
        options: [
          "Under AED 20,000",
          "AED 20,000 - 50,000",
          "AED 50,000 - 100,000",
          "AED 100,000 - 200,000",
          "AED 200,000 - 500,000",
          "Over AED 500,000",
        ],
      },
      {
        id: "year",
        label: "Year",
        type: "select",
        placeholder: "Any Year",
        options: [
          "2024",
          "2023",
          "2022",
          "2021",
          "2020",
          "2019",
          "2018",
          "2017",
          "2016",
          "2015 & Older",
        ],
      },
    ],
  },

  property: {
    name: "Property for Rent",
    icon: "🏠",
    filters: [
      {
        id: "location",
        label: "Location",
        type: "select",
        placeholder: "All UAE",
        options: [
          "Dubai",
          "Abu Dhabi",
          "Sharjah",
          "Ajman",
          "Ras Al Khaimah",
          "Fujairah",
        ],
      },
      {
        id: "type",
        label: "Property Type",
        type: "select",
        placeholder: "All Types",
        options: [
          "Apartment",
          "Villa",
          "Townhouse",
          "Studio",
          "Penthouse",
          "Office",
          "Shop",
          "Warehouse",
        ],
      },
      {
        id: "bedrooms",
        label: "Bedrooms",
        type: "select",
        placeholder: "Any",
        options: ["Studio", "1 BR", "2 BR", "3 BR", "4 BR", "5+ BR"],
      },
      {
        id: "price",
        label: "Price (AED/Year)",
        type: "select",
        placeholder: "Any Price",
        options: [
          "Under 30,000",
          "30,000 - 60,000",
          "60,000 - 100,000",
          "100,000 - 200,000",
          "200,000+",
        ],
      },
      {
        id: "area",
        label: "Area (Sq Ft)",
        type: "select",
        placeholder: "Any Size",
        options: [
          "Under 500",
          "500 - 1,000",
          "1,000 - 2,000",
          "2,000 - 3,000",
          "3,000+",
        ],
      },
    ],
  },

  electronics: {
    name: "Electronics",
    icon: "📱",
    filters: [
      {
        id: "location",
        label: "Location",
        type: "select",
        placeholder: "All UAE",
        options: ["Dubai", "Abu Dhabi", "Sharjah", "Ajman"],
      },
      {
        id: "category",
        label: "Category",
        type: "select",
        placeholder: "All Categories",
        options: [
          "Mobile Phones",
          "Computers & Laptops",
          "Cameras & Photography",
          "Audio & Music",
          "Video Games & Consoles",
          "TV & Home Appliances",
        ],
      },
      {
        id: "brand",
        label: "Brand",
        type: "search",
        placeholder: "Search Brand...",
        options: [
          "Apple",
          "Samsung",
          "Sony",
          "LG",
          "Dell",
          "HP",
          "Canon",
          "Nikon",
        ],
      },
      {
        id: "condition",
        label: "Condition",
        type: "select",
        placeholder: "Any Condition",
        options: ["New", "Used - Like New", "Used - Good", "Used - Fair"],
      },
      {
        id: "price",
        label: "Price",
        type: "select",
        placeholder: "Any Price",
        options: [
          "Under AED 500",
          "AED 500 - 1,000",
          "AED 1,000 - 2,500",
          "AED 2,500 - 5,000",
          "Over AED 5,000",
        ],
      },
    ],
  },

  jobs: {
    name: "Jobs",
    icon: "💼",
    filters: [
      {
        id: "location",
        label: "Location",
        type: "select",
        placeholder: "All UAE",
        options: ["Dubai", "Abu Dhabi", "Sharjah", "Ajman"],
      },
      {
        id: "category",
        label: "Job Category",
        type: "select",
        placeholder: "All Categories",
        options: [
          "Accounting & Finance",
          "Engineering",
          "IT & Software",
          "Sales & Marketing",
          "Healthcare",
          "Education",
          "Hospitality",
        ],
      },
      {
        id: "experience",
        label: "Experience",
        type: "select",
        placeholder: "Any Experience",
        options: [
          "Fresh Graduate",
          "1-2 Years",
          "3-5 Years",
          "5-10 Years",
          "10+ Years",
        ],
      },
      {
        id: "salary",
        label: "Salary (AED/Month)",
        type: "select",
        placeholder: "Any Salary",
        options: [
          "Under 5,000",
          "5,000 - 10,000",
          "10,000 - 20,000",
          "20,000 - 50,000",
          "50,000+",
        ],
      },
      {
        id: "type",
        label: "Job Type",
        type: "select",
        placeholder: "Any Type",
        options: [
          "Full Time",
          "Part Time",
          "Contract",
          "Freelance",
          "Internship",
        ],
      },
    ],
  },
};

interface FilterState {
  [key: string]: string;
}

interface FilterDropdownProps {
  filter: {
    id: string;
    label: string;
    type: string;
    placeholder: string;
    options: string[];
  };
  value: string;
  onChange: (value: string) => void;
  onClear: () => void;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  filter,
  value,
  onChange,
  onClear,
}) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredOptions = filter.options.filter((option) =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-w-[140px] sm:min-w-[160px] lg:min-w-[180px] lg:max-w-[220px] flex-shrink-0">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={`w-full justify-between text-left h-10 sm:h-12 px-3 sm:px-4 text-xs sm:text-sm ${
              value
                ? "border-primary bg-primary/5 text-primary hover:bg-primary/10 hover:text-white"
                : "border-gray-200 hover:border-primary/50 hover:text-white hover:bg-primary"
            }`}
          >
            <div className="flex flex-col items-start min-w-0 flex-1">
              <span className="text-[3px] sm:text-[8px] font-medium uppercase tracking-wide ">
                {filter.label}
              </span>
              <span className="text-xs sm:text-sm font-medium truncate w-full">
                {value || filter.placeholder}
              </span>
            </div>
            <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400 flex-shrink-0 ml-1 sm:ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-56 sm:w-64 bg-white text-primary border border-gray-200 shadow-lg"
          align="start"
        >
          {filter.type === "search" && (
            <div className="p-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={`Search ${filter.label.toLowerCase()}...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 text-sm border-gray-200 focus:border-primary"
                />
              </div>
            </div>
          )}

          {value && (
            <>
              <DropdownMenuItem
                onClick={onClear}
                className="text-red-600 hover:bg-red-50 hover:text-red-700 cursor-pointer font-medium"
              >
                <X className="h-4 w-4 mr-2" />
                Clear {filter.label}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
            </>
          )}

          <div className="max-h-48 overflow-y-auto">
            {filteredOptions.map((option) => (
              <DropdownMenuItem
                key={option}
                onClick={() => onChange(option)}
                className={`cursor-pointer text-sm ${
                  value === option
                    ? "bg-primary text-white hover:bg-primary/90"
                    : "hover:bg-gray-50"
                }`}
              >
                {option}
              </DropdownMenuItem>
            ))}
            {filteredOptions.length === 0 && filter.type === "search" && (
              <div className="px-3 py-2 text-gray-500 text-sm">
                No results found
              </div>
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

interface CategoryDropdownFilterComponentProps
  extends React.HTMLAttributes<"div"> {
  category: string;
}

export default function CategorydropdownfilterComponet({
  category,
}: CategoryDropdownFilterComponentProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>(category);
  const [filters, setFilters] = useState<FilterState>({});
  const [showAllFilters, setShowAllFilters] = useState<boolean>(false);

  const currentConfig =
    CATEGORY_CONFIGS[selectedCategory as keyof typeof CATEGORY_CONFIGS];

  // Show different number of filters based on screen size
  const hiddenFiltersCount = Math.max(0, currentConfig.filters.length - 4);
  const activeFiltersCount = Object.keys(filters).length;

  // Reset filters when category changes
  useEffect(() => {
    setFilters({});
    setShowAllFilters(false);
  }, [selectedCategory]);

  const handleFilterChange = (filterId: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [filterId]: value,
    }));
  };

  const handleFilterClear = (filterId: string) => {
    setFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[filterId];
      return newFilters;
    });
  };

  const clearAllFilters = () => {
    setFilters({});
  };

  const handleSearch = () => {
    console.log("Searching with filters:", {
      category: selectedCategory,
      filters,
    });
    // Implement your search logic here
  };

  return (
    <div className="bg-white border-b border-gray-100 top-0 z-40 shadow-sm">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-3">
        {/* Filter Controls */}
        <div className="space-y-3 sm:space-y-4">
          <div className="flex items-end gap-3">
            <div className="flex gap-2 sm:gap-3 overflow-x-auto scrollbar-hide pb-1 flex-1 min-w-0">
              <div className="flex gap-2 sm:gap-3 items-end">
                {currentConfig.filters.map((filter) => (
                  <FilterDropdown
                    key={filter.id}
                    filter={filter}
                    value={filters[filter.id] || ""}
                    onChange={(value) => handleFilterChange(filter.id, value)}
                    onClear={() => handleFilterClear(filter.id)}
                  />
                ))}
              </div>
            </div>

            {/* Action buttons - Always visible */}
            <Button
              onClick={handleSearch}
              className="bg-primary hover:bg-primary/90 text-white h-10 sm:h-12 font-semibold px-4 sm:px-6 text-xs sm:text-sm whitespace-nowrap mb-1"
            >
              <Search className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              Search
            </Button>
          </div>

          {/* Active Filters Display */}
          {activeFiltersCount > 0 && (
            <div className="pt-2 border-t border-gray-100">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs sm:text-sm font-medium text-gray-600">
                  Active Filters ({activeFiltersCount})
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 text-xs sm:text-sm p-1 h-6 sm:h-7"
                >
                  Clear All
                </Button>
              </div>
              {/* Scrollable Active Filters */}
              <div className="flex gap-1 sm:gap-2 overflow-x-auto scrollbar-hide pb-1">
                <div className="flex gap-1 sm:gap-2">
                  {Object.entries(filters).map(([filterId, value]) => {
                    const filter = currentConfig.filters.find(
                      (f) => f.id === filterId
                    );
                    return (
                      <Badge
                        key={filterId}
                        variant="secondary"
                        className="bg-primary/10 text-primary hover:bg-primary/20 cursor-pointer text-xs py-1 px-2 whitespace-nowrap flex-shrink-0"
                        onClick={() => handleFilterClear(filterId)}
                      >
                        <span className="hidden sm:inline">
                          {filter?.label}:{" "}
                        </span>
                        {value}
                        <X className="h-3 w-3 ml-1 flex-shrink-0" />
                      </Badge>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <style jsx global>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}
