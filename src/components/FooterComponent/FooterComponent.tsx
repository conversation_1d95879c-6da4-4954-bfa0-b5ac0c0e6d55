import Link from "next/link";
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";
import Image from "next/image";

export default function FooterComponent() {
  return (
    <footer className="bg-white border-t border-gray-200 mt-auto">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Image
                src="/images/eastclassified.jpeg"
                alt="East Classifieds Logo"
                width={40}
                height={40}
                className="rounded-l-3xl"
              ></Image>
              <span className="text-2xl font-bold text-primary">
                East Classifieds
              </span>
            </div>
            <p className="text-gray-600 text-sm leading-relaxed">
              Your trusted marketplace for buying and selling. Connect with
              buyers and sellers in your area with ease and confidence.
            </p>
            <div className="flex space-x-4">
              <Link
                href="#"
                className="text-gray-400 hover:text-primary transition-colors duration-200"
              >
                <Facebook size={20} />
              </Link>
              <Link
                href="#"
                className="text-gray-400 hover:text-primary transition-colors duration-200"
              >
                <Twitter size={20} />
              </Link>
              <Link
                href="#"
                className="text-gray-400 hover:text-primary transition-colors duration-200"
              >
                <Instagram size={20} />
              </Link>
              <Link
                href="#"
                className="text-gray-400 hover:text-primary transition-colors duration-200"
              >
                <Linkedin size={20} />
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-primary">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/categories"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  All Categories
                </Link>
              </li>
              <li>
                <Link
                  href="/post-ad"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Post Free Ad
                </Link>
              </li>
              <li>
                <Link
                  href="/featured"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Featured Ads
                </Link>
              </li>
              <li>
                <Link
                  href="/search"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Advanced Search
                </Link>
              </li>
              <li>
                <Link
                  href="/membership"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Membership Plans
                </Link>
              </li>
            </ul>
          </div>

          {/* Support & Help */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-primary">
              Support & Help
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/help"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Help Center
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Contact Us
                </Link>
              </li>
              <li>
                <Link
                  href="/safety"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Safety Tips
                </Link>
              </li>
              <li>
                <Link
                  href="/faq"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  FAQ
                </Link>
              </li>
              <li>
                <Link
                  href="/report"
                  className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm"
                >
                  Report Issue
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-primary">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin
                  size={16}
                  className="text-primary mt-0.5 flex-shrink-0"
                />
                <span className="text-gray-600 text-sm">
                  123 Business Street, City Center, State 12345
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone size={16} className="text-primary flex-shrink-0" />
                <span className="text-gray-600 text-sm">+****************</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail size={16} className="text-primary flex-shrink-0" />
                <span className="text-gray-600 text-sm">
                  <EMAIL>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Categories Section */}
      {/* <div className="bg-gray-50 border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h3 className="text-lg font-semibold text-primary mb-4">Popular Categories</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {[
              'Cars', 'Electronics', 'Real Estate', 'Jobs', 'Furniture', 'Mobile Phones',
              'Fashion', 'Sports', 'Books', 'Services', 'Pets', 'Home & Garden'
            ].map((category) => (
              <Link
                key={category}
                href={`/category/${category.toLowerCase().replace(' ', '-')}`}
                className="text-gray-600 hover:text-primary transition-colors duration-200 text-sm py-1"
              >
                {category}
              </Link>
            ))}
          </div>
        </div>
      </div> */}

      {/* Bottom Footer */}
      <div className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <p className="text-gray-600 text-sm">
                © 2024 EastClassified. All rights reserved.
              </p>
              <div className="flex space-x-4">
                <Link
                  href="/privacy"
                  className="text-gray-500 hover:text-primary transition-colors duration-200 text-xs"
                >
                  Privacy Policy
                </Link>
                <Link
                  href="/terms"
                  className="text-gray-500 hover:text-primary transition-colors duration-200 text-xs"
                >
                  Terms of Service
                </Link>
                <Link
                  href="/cookies"
                  className="text-gray-500 hover:text-primary transition-colors duration-200 text-xs"
                >
                  Cookie Policy
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-500 text-xs">Available on:</span>
              <div className="flex space-x-2">
                <div className="px-3 py-1 bg-gray-100 rounded text-xs text-gray-600">
                  iOS
                </div>
                <div className="px-3 py-1 bg-gray-100 rounded text-xs text-gray-600">
                  Android
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
