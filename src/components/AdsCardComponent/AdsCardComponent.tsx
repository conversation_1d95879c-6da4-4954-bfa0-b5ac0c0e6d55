"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  Heart,
  Camera,
  MapPin,
  Calendar,
  Gauge,
  Settings,
  Globe,
  Building,
  Bed,
  Bath,
  Maximize,
  Camera as CameraIcon,
  Star,
  User,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CarAd, ElectronicsAd, JobAd, PropertyAd } from "@/model/adsModel";

// Define types for different ad categories
type Ad = CarAd | PropertyAd | ElectronicsAd | JobAd;

interface AdsCardComponentProps {
  ad: Ad;
  onFavoriteClick?: (id: string) => void;
  onCardClick?: (id: string) => void;
  isListView?: boolean;
}

export default function AdsCardComponent({
  ad,
  onFavoriteClick,
  onCardClick,
  isListView = false,
}: AdsCardComponentProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onFavoriteClick?.(ad.id);
  };

  const handleCardClick = () => {
    onCardClick?.(ad.id);
  };

  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex((prev) =>
      prev === ad.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex((prev) =>
      prev === 0 ? ad.images.length - 1 : prev - 1
    );
  };

  // Render category-specific details with responsive sizing
  const renderCategoryDetails = () => {
    const titleClass = isListView 
      ? "font-semibold text-md md:text-lg text-gray-900 line-clamp-1"
      : "font-semibold text-base md:text-md text-gray-900 line-clamp-1";
    
    const descClass = isListView
      ? "text-gray-600 text-sm md:text-base line-clamp-2 mb-2 md:mb-3"
      : "text-gray-600 text-xs md:text-sm line-clamp-2 mb-2";
    
    const detailsClass = isListView
      ? "flex flex-wrap items-center gap-3 md:gap-4 text-sm md:text-base text-gray-600"
      : "flex flex-wrap items-center gap-2 md:gap-3 text-xs md:text-sm text-gray-600";
    
    const iconClass = isListView ? "h-4 w-4 md:h-5 md:w-5" : "h-3 w-3 md:h-4 md:w-4";

    switch (ad.category) {
      case "car":
        const carAd = ad as CarAd;
        return (
          <div className="space-y-2 md:space-y-3">
            <h3 className={titleClass}>
              {carAd.brand} • {carAd.model} • {carAd.transmission || "Manual"}
            </h3>

            <p className={descClass}>
              {carAd.description}
            </p>

            <div className={detailsClass}>
              <div className="flex items-center gap-1">
                <Calendar className={iconClass} />
                <span>{carAd.year}</span>
              </div>
              <div className="flex items-center gap-1">
                <Gauge className={iconClass} />
                <span>{carAd.mileage.toLocaleString()} km</span>
              </div>
              <div className="flex items-center gap-1">
                <Settings className={iconClass} />
                <span>{carAd.transmission || "Manual"}</span>
              </div>
              {isListView && carAd.specs && (
                <div className="flex items-center gap-1">
                  <Globe className={iconClass} />
                  <span>{carAd.specs}</span>
                </div>
              )}
            </div>
          </div>
        );

      case "property":
        const propertyAd = ad as PropertyAd;
        return (
          <div className="space-y-2 md:space-y-3">
            <h3 className={titleClass}>
              {propertyAd.propertyType}
            </h3>

            <p className={descClass}>
              {propertyAd.description}
            </p>

            <div className={detailsClass}>
              {propertyAd.bedrooms && (
                <div className="flex items-center gap-1">
                  <Bed className={iconClass} />
                  <span>{propertyAd.bedrooms} BR</span>
                </div>
              )}
              {propertyAd.bathrooms && (
                <div className="flex items-center gap-1">
                  <Bath className={iconClass} />
                  <span>{propertyAd.bathrooms} Bath</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <Maximize className={iconClass} />
                <span>
                  {propertyAd.area.toLocaleString()} {propertyAd.areaUnit}
                </span>
              </div>
              {isListView && propertyAd.furnished && (
                <div className="flex items-center gap-1">
                  <Building className={iconClass} />
                  <span>Furnished</span>
                </div>
              )}
            </div>
          </div>
        );

      case "electronics":
        const electronicsAd = ad as ElectronicsAd;
        return (
          <div className="space-y-2 md:space-y-3">
            <h3 className={titleClass}>
              {electronicsAd.brand} • {electronicsAd.model}
              {!isListView && electronicsAd.subcategory && ` • ${electronicsAd.subcategory}`}
            </h3>

            <p className={descClass}>
              {electronicsAd.description}
            </p>

            <div className={detailsClass}>
              <div className="flex items-center gap-1">
                <Star className={iconClass} />
                <span>{electronicsAd.condition}</span>
              </div>
              {electronicsAd.warranty && (
                <div className="flex items-center gap-1">
                  <Settings className={iconClass} />
                  <span>Warranty</span>
                </div>
              )}
              {isListView && electronicsAd.accessories && (
                <div className="flex items-center gap-1">
                  <CameraIcon className={iconClass} />
                  <span>Accessories</span>
                </div>
              )}
            </div>
          </div>
        );

      case "job":
        const jobAd = ad as JobAd;
        return (
          <div className="space-y-2 md:space-y-3">
            <h3 className={titleClass}>
              {jobAd.company} • {jobAd.jobType}
            </h3>

            <p className={descClass}>
              {jobAd.description}
            </p>

            <div className={detailsClass}>
              <div className="flex items-center gap-1">
                <User className={iconClass} />
                <span>{jobAd.experience}</span>
              </div>
              <div className="flex items-center gap-1">
                <Building className={iconClass} />
                <span>{jobAd.jobType}</span>
              </div>
              {isListView && jobAd.benefits && (
                <div className="flex items-center gap-1">
                  <Star className={iconClass} />
                  <span>Benefits</span>
                </div>
              )}
            </div>
          </div>
        );

      default:
        const defaultAd = ad as Ad;
        return (
          <div className="space-y-2 md:space-y-3">
            <h3 className={titleClass}>
              {defaultAd.title}
            </h3>
            <p className={descClass}>
              {defaultAd.description}
            </p>
          </div>
        );
    }
  };

  if (isListView) {
    return (
      <div
        className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer group flex flex-col sm:flex-row h-auto sm:h-60"
        onClick={handleCardClick}
      >
        {/* Image Section - List View */}
        <div className="relative w-full sm:w-72 md:w-80 flex-shrink-0">
          <div className="aspect-video sm:aspect-[4/3] relative overflow-hidden sm:h-60">
            <Image
              src={ad.images[currentImageIndex]}
              alt={ad.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />

            {/* Image Navigation */}
            {ad.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </>
            )}

            {/* Overlay Elements */}
            <div className="absolute top-2 left-2 flex flex-col gap-1">
              {ad.isPremium && (
                <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-bold px-1.5 py-0.5">
                  PREMIUM
                </Badge>
              )}
              {ad.isPromoted && (
                <Badge className="bg-blue-500 hover:bg-blue-600 text-white text-xs font-bold px-1.5 py-0.5">
                  PROMOTED
                </Badge>
              )}
            </div>

            <div className="absolute top-2 right-2">
              <Button
                variant="ghost"
                size="sm"
                className={`rounded-full p-1.5 h-auto ${
                  ad.isFavorite
                    ? "bg-red-500 hover:bg-red-600 text-white"
                    : "bg-white/80 hover:bg-white text-gray-600 hover:text-red-500"
                }`}
                onClick={handleFavoriteClick}
              >
                <Heart
                  className="h-3.5 w-3.5"
                  fill={ad.isFavorite ? "currentColor" : "none"}
                />
              </Button>
            </div>

            <div className="absolute bottom-2 left-2">
              <div className="flex items-center gap-1 bg-black/70 text-white rounded px-1.5 py-0.5 text-xs">
                <Camera className="h-3 w-3" />
                <span>{ad.images.length}</span>
              </div>
            </div>

            {/* Image Dots */}
            {ad.images.length > 1 && (
              <div className="absolute bottom-2 right-2 flex gap-0.5">
                {ad.images.map((_, index) => (
                  <div
                    key={index}
                    className={`w-1.5 h-1.5 rounded-full transition-colors ${
                      index === currentImageIndex ? "bg-white" : "bg-white/50"
                    }`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Content Section - List View */}
        <div className="flex-1 p-3 md:p-4 flex flex-col justify-between sm:h-60">
          <div className="flex-1">
            {/* Price */}
            <div className="flex items-start justify-between mb-2">
              <div className="text-lg md:text-xl font-bold text-gray-900">
                {ad.currency} {ad.price.toLocaleString()}
              </div>
              {ad.sellerLogo && (
                <div className="w-6 h-6 md:w-8 md:h-8 ml-2">
                  <Image
                    src={ad.sellerLogo}
                    alt="Seller Logo"
                    width={32}
                    height={32}
                    className="object-contain"
                  />
                </div>
              )}
            </div>

            {/* Category-specific Details */}
            {renderCategoryDetails()}
          </div>

          {/* Location and Date */}
          <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-100">
            <div className="flex items-center gap-1 text-xs md:text-sm text-gray-600">
              <MapPin className="h-3 w-3 md:h-4 md:w-4" />
              <span className="truncate">{ad.location}</span>
            </div>
            <div className="text-xs md:text-sm text-gray-500 ml-2">
              {ad.postedDate}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid View (Default)
  return (
    <div
      className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer group max-w-sm h-120 flex flex-col"
      onClick={handleCardClick}
    >
      {/* Image Section - Grid View */}
      <div className="relative flex-shrink-0">
        <div className="aspect-[4/3] relative overflow-hidden h-56">
          <Image
            src={ad.images[currentImageIndex]}
            alt={ad.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />

          {/* Image Navigation */}
          {ad.images.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <button
                onClick={nextImage}
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </>
          )}
        </div>

        {/* Overlay Elements */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {ad.isPremium && (
            <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-bold px-1.5 py-0.5">
              PREMIUM
            </Badge>
          )}
          {ad.isPromoted && (
            <Badge className="bg-blue-500 hover:bg-blue-600 text-white text-xs font-bold px-1.5 py-0.5">
              PROMOTED
            </Badge>
          )}
        </div>

        <div className="absolute top-2 right-2">
          <Button
            variant="ghost"
            size="sm"
            className={`rounded-full p-1.5 h-auto ${
              ad.isFavorite
                ? "bg-red-500 hover:bg-red-600 text-white"
                : "bg-white/80 hover:bg-white text-gray-600 hover:text-red-500"
            }`}
            onClick={handleFavoriteClick}
          >
            <Heart
              className="h-3.5 w-3.5"
              fill={ad.isFavorite ? "currentColor" : "none"}
            />
          </Button>
        </div>

        <div className="absolute bottom-2 left-2">
          <div className="flex items-center gap-1 bg-black/70 text-white rounded px-1.5 py-0.5 text-xs">
            <Camera className="h-3 w-3" />
            <span>{ad.images.length}</span>
          </div>
        </div>

        {/* Image Dots */}
        {ad.images.length > 1 && (
          <div className="absolute bottom-2 right-2 flex gap-0.5">
            {ad.images.map((_, index) => (
              <div
                key={index}
                className={`w-1.5 h-1.5 rounded-full transition-colors ${
                  index === currentImageIndex ? "bg-white" : "bg-white/50"
                }`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Content Section - Grid View */}
      <div className="p-3 flex flex-col justify-between flex-1">
        <div className="flex-1">
          {/* Price */}
          <div className="flex items-center justify-between mb-2">
            <div className="text-lg md:text-sm font-bold text-gray-900">
              {ad.currency} {ad.price.toLocaleString()}
            </div>
            {ad.sellerLogo && (
              <div className="w-5 h-5 md:w-6 md:h-6">
                <Image
                  src={ad.sellerLogo}
                  alt="Seller Logo"
                  width={24}
                  height={24}
                  className="object-contain"
                />
              </div>
            )}
          </div>

          {/* Category-specific Details */}
          {renderCategoryDetails()}
        </div>

        {/* Location and Date */}
        <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-100">
          <div className="flex items-center gap-1 text-xs text-gray-600">
            <MapPin className="h-3 w-3" />
            <span className="truncate">{ad.location}</span>
          </div>
          <div className="text-xs text-gray-500">
            {ad.postedDate}
          </div>
        </div>
      </div>
    </div>
  );
}