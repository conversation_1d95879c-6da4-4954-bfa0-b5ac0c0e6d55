interface BaseAd {
  id: string;
  title: string;
  price: number;
  currency: string;
  location: string;
  images: string[];
  isPremium?: boolean;
  isPromoted?: boolean;
  isFavorite?: boolean;
  postedDate: string;
  description?: string;
  sellerLogo?: string;
}

export interface CarAd extends BaseAd {
  category: "car";
  brand: string;
  model: string;
  year: number;
  mileage: number;
  fuelType?: string;
  transmission?: string;
  specs?: string;
  warranty?: boolean;
  finance?: boolean;
}

export interface PropertyAd extends BaseAd {
  category: "property";
  propertyType: string;
  bedrooms?: number;
  bathrooms?: number;
  area: number;
  areaUnit: string;
  furnished?: boolean;
  parking?: boolean;
  balcony?: boolean;
}

export interface ElectronicsAd extends BaseAd {
  category: "electronics";
  brand: string;
  model?: string;
  condition: string;
  warranty?: boolean;
  accessories?: boolean;
  subcategory: string;
}

export interface JobAd extends BaseAd {
  category: "job";
  company: string;
  jobType: string;
  experience: string;
  salary?: number;
  benefits?: string[];
}

type Ad = CarAd | PropertyAd | ElectronicsAd | JobAd;
