eastclassified/
├── public/
│   ├── images/
│   │   ├── logo.png
│   │   ├── emirates/
│   │   │   ├── dubai.jpg
│   │   │   ├── abu-dhabi.jpg
│   │   │   ├── sharjah.jpg
│   │   │   └── ajman.jpg
│   │   └── categories/
│   ├── icons/
│   └── favicon.ico
│
├── src/
│   ├── app/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   ├── page.tsx                    # Homepage (emirate selection)
│   │   ├── loading.tsx
│   │   ├── not-found.tsx
│   │   │
│   │   ├── [emirate]/                  # Dynamic route for emirates
│   │   │   ├── page.tsx                # Emirate homepage
│   │   │   ├── layout.tsx
│   │   │   │
│   │   │   ├── [category]/             # Categories within emirate
│   │   │   │   ├── page.tsx
│   │   │   │   └── [subcategory]/
│   │   │   │       └── page.tsx
│   │   │   │
│   │   │   ├── search/
│   │   │   │   └── page.tsx
│   │   │   │
│   │   │   ├── post-ad/
│   │   │   │   ├── page.tsx
│   │   │   │   └── [step]/
│   │   │   │       └── page.tsx
│   │   │   │
│   │   │   └── ad/
│   │   │       └── [id]/
│   │   │           ├── page.tsx        # Ad detail page
│   │   │           └── edit/
│   │   │               └── page.tsx
│   │   │
│   │   ├── profile/
│   │   │   ├── page.tsx
│   │   │   ├── my-ads/
│   │   │   │   └── page.tsx
│   │   │   ├── favorites/
│   │   │   │   └── page.tsx
│   │   │   └── settings/
│   │   │       └── page.tsx
│   │   │
│   │   ├── auth/
│   │   │   ├── login/
│   │   │   │   └── page.tsx
│   │   │   └── register/
│   │   │       └── page.tsx
│   │
│   ├── components/
│   │   ├── ui/                         # Reusable UI components
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   ├── Modal.tsx
│   │   │   ├── Card.tsx
│   │   │   ├── Badge.tsx
│   │   │   └── Dropdown.tsx
│   │   │
│   │   ├── layout/
│   │   │   ├── Header.tsx
│   │   │   ├── Footer.tsx
│   │   │   ├── Navbar.tsx
│   │   │   └── Sidebar.tsx
│   │   │
│   │   ├── home/
│   │   │   ├── EmirateCard.tsx
│   │   │   ├── HeroSection.tsx
│   │   │   └── FeaturedCategories.tsx
│   │   │
│   │   ├── ads/
│   │   │   ├── AdCard.tsx
│   │   │   ├── AdGrid.tsx
│   │   │   ├── AdDetail.tsx
│   │   │   ├── AdForm.tsx
│   │   │   └── ImageUploader.tsx
│   │   │
│   │   ├── search/
│   │   │   ├── SearchBar.tsx
│   │   │   ├── FilterSidebar.tsx
│   │   │   └── SortOptions.tsx
│   │   │
│   │   └── auth/
│   │       ├── LoginForm.tsx
│   │       ├── RegisterForm.tsx
│   │       └── AuthGuard.tsx
│   │
│   ├── lib/
│   │   ├── auth.ts                     # Authentication logic
│   │   ├── utils.ts                    # Utility functions
│   │   ├── validations.ts              # Form validations
│   │   └── constants.ts                # App constants
│   │
│   ├── hooks/
│   │   ├── useAuth.ts
│   │   ├── useLocalStorage.ts
│   │   └── useDebounce.ts
│   │
│   ├── store/                          # State management (if using Redux/Zustand)
│   │   ├── authSlice.ts
│   │   ├── adsSlice.ts
│   │   └── index.ts
│   │
│   ├── types/
│   │   ├── index.ts
│   │   ├── ad.ts
│   │   ├── user.ts
│   │   └── api.ts
│   │
│   └── styles/
│       ├── globals.css
│       └── components.css
│
├── .env.local
├── .env.example
├── .gitignore
├── next.config.js
├── package.json
├── tailwind.config.js                  # If using Tailwind CSS
├── tsconfig.json
└── README.md